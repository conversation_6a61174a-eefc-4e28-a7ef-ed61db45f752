//
//  SmartPrompterApp.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/19.
//

import SwiftUI
import SwiftData

@main
struct SmartPrompterApp: App {
    
    @State private var scriptManager: ScriptManager?

    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            Folder.self,
            Note.self
        ])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }()
    
    init() {
        let context = sharedModelContainer.mainContext
        let manager = ScriptManager(modelContext: context)
        _scriptManager = State(initialValue: manager)

        // 预加载触觉反馈生成器，提高首次反馈的响应速度
        AppHaptic.prepareAll()
    }

    var body: some Scene {
        WindowGroup {
            if let scriptManager = scriptManager {
                NoteView()
                    .environment(scriptManager)
            } else {
                ProgressView()
            }
        }
        .modelContainer(sharedModelContainer)
    }

}

