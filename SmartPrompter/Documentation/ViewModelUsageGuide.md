# ViewModel 使用指南

## 📚 概述

SmartPrompter 使用 MVVM 架构模式，通过 ViewModel 来管理 UI 状态和业务逻辑。

## 🏗️ 架构说明

### 数据流向
```
View (UI) ↔ ViewModel (状态管理) ↔ Manager (数据操作) ↔ SwiftData (数据存储)
```

### 主要组件
- **NoteView** - 主界面视图
- **NoteViewModel** - 状态管理和UI逻辑
- **NoteManager** - 数据操作和业务逻辑
- **ScriptManager** - 全局数据管理

## 🎯 实际使用示例

### 1. 在 View 中使用 ViewModel

```swift
struct NoteView: View {
    @Environment(ScriptManager.self) var scriptManager
    @State var viewModel = NoteViewModel()  // 创建 ViewModel 实例
    
    var body: some View {
        VStack {
            // 使用 ViewModel 的数据
            ForEach(viewModel.displayedNotes) { note in
                NoteItemView(note: note)
            }
        }
        .onAppear {
            // 初始化时设置数据源
            viewModel.setScriptManager(scriptManager)
        }
    }
}
```

### 2. 调用 ViewModel 方法改变状态

```swift
// 在按钮点击事件中调用 ViewModel 方法
Button("按字数排序") {
    viewModel.sortByWordCountDescending()  // 设置为字数降序
}

Button("按创建时间排序") {
    viewModel.sortByCreatedDateDescending()  // 设置为创建时间降序
}

Button("切换收藏筛选") {
    viewModel.isInlikeMode.toggle()  // 直接修改状态属性
}
```

### 3. 响应 ViewModel 状态变化

```swift
// UI 会自动响应 ViewModel 状态变化
Text("当前排序: \(viewModel.noteSortMode.displayName)")
Text("排序方向: \(viewModel.isAscending ? "升序" : "降序")")

// 根据状态显示不同内容
if viewModel.isInlikeMode {
    Text("只显示收藏的笔记")
} else {
    Text("显示所有笔记")
}
```

## 🔧 ViewModel 方法详解

### 排序相关方法

```swift
// 设置排序方式（通用方法）
viewModel.setSortMode(.wordCount, ascending: false)

// 便捷方法
viewModel.sortByWordCountDescending()     // 按字数降序
viewModel.sortByCreatedDateDescending()   // 按创建时间降序
viewModel.sortByTitleAscending()          // 按标题升序

// 切换排序顺序
viewModel.toggleSortOrder()
```

### 搜索相关方法

```swift
// 设置搜索关键词（会自动触发搜索）
viewModel.searchText = "关键词"

// 清除搜索
viewModel.clearSearch()

// 获取搜索状态
if viewModel.isSearching {
    // 显示搜索结果
}
```

### 选择相关方法

```swift
// 选择笔记
viewModel.selectNote(note)

// 获取当前选中的笔记
if let selectedNote = viewModel.selectedNote {
    // 处理选中的笔记
}
```

## 📊 状态属性说明

### 数据属性
- `notes: [Note]` - 当前文件夹的所有笔记
- `folders: [Folder]` - 所有文件夹
- `selectedNote: Note?` - 当前选中的笔记
- `selectedFolder: Folder?` - 当前选中的文件夹

### 状态属性
- `noteSortMode: NoteSortType` - 当前排序方式
- `isAscending: Bool` - 是否升序排列
- `isInlikeMode: Bool` - 是否只显示收藏
- `searchText: String` - 搜索关键词
- `isSearching: Bool` - 是否正在搜索（只读）

### 计算属性
- `displayedNotes: [Note]` - 显示的笔记列表（已排序和筛选）
- `searchResults: [Note]` - 搜索结果

## 🎨 在菜单中使用 ViewModel

```swift
Button("菜单") { }
    .addDropdownMenu {
        // 排序选项
        AppMenuManager.createMenuItem("按字数排序", icon: "textformat.123") {
            viewModel.sortByWordCountDescending()
        }
        
        AppMenuManager.createMenuItem("按时间排序", icon: "calendar") {
            viewModel.sortByCreatedDateDescending()
        }
        
        AppMenuManager.createDivider()
        
        // 筛选选项
        AppMenuManager.createMenuItem(
            viewModel.isInlikeMode ? "显示所有" : "只显示收藏",
            icon: "heart"
        ) {
            viewModel.isInlikeMode.toggle()
        }
    }
```

## 🔄 数据绑定原理

### @Observable 装饰器
```swift
@Observable
class NoteViewModel {
    var notes: [Note] = []  // 当这个属性改变时，UI 会自动更新
}
```

### 自动更新机制
1. ViewModel 使用 `@Observable` 装饰器
2. View 中使用 `@State` 创建 ViewModel 实例
3. 当 ViewModel 的属性发生变化时，SwiftUI 自动重新渲染相关的 UI

### 最佳实践
```swift
// ✅ 正确：通过方法修改状态
viewModel.sortByWordCountDescending()

// ✅ 正确：直接修改简单属性
viewModel.isInlikeMode = true

// ❌ 避免：直接修改复杂数据
viewModel.notes.append(newNote)  // 应该通过 Manager 操作

// ✅ 正确：通过 Manager 操作数据
scriptManager.noteManager.createNote(title: "新笔记", content: "内容")
viewModel.requestAllNotesAndFolder()  // 然后刷新 ViewModel
```

## 🚀 完整示例

```swift
struct ExampleView: View {
    @State var viewModel = NoteViewModel()
    
    var body: some View {
        VStack {
            // 显示当前状态
            HStack {
                Text("排序: \(viewModel.noteSortMode.displayName)")
                Text("方向: \(viewModel.isAscending ? "↑" : "↓")")
                if viewModel.isInlikeMode {
                    Image(systemName: "heart.fill")
                }
            }
            
            // 笔记列表
            List(viewModel.displayedNotes) { note in
                VStack(alignment: .leading) {
                    Text(note.title)
                    Text("\(note.content.count) 字")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .onTapGesture {
                    viewModel.selectNote(note)
                }
            }
            
            // 控制按钮
            HStack {
                Button("字数排序") {
                    viewModel.sortByWordCountDescending()
                }
                
                Button("时间排序") {
                    viewModel.sortByCreatedDateDescending()
                }
                
                Button("切换收藏") {
                    viewModel.isInlikeMode.toggle()
                }
            }
        }
    }
}
```

这样您就可以轻松地在 UI 中调用 ViewModel 的方法来改变应用状态了！
