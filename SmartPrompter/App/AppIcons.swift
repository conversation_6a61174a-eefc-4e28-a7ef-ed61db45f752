//
//  AppIcons.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/19.
//

import SwiftUI

// MARK: - 应用图标系统
/// 统一的图标定义，确保整个应用的图标使用一致性

struct AppIcons {
    
    // MARK: - 导航图标
    /// 导航相关图标
    struct Navigation {
        static let back = "chevron.left"
        static let forward = "chevron.right"
        static let close = "xmark"
        static let menu = "line.3.horizontal"
        static let more = "ellipsis"
        static let search = "magnifyingglass"
        static let filter = "line.3.horizontal.decrease.circle"
    }
    
    // MARK: - 操作图标
    /// 操作相关图标
    struct Action {
        static let add = "plus"
        static let edit = "pencil"
        static let delete = "trash"
        static let save = "checkmark"
        static let cancel = "xmark"
        static let share = "square.and.arrow.up"
        static let copy = "doc.on.doc"
        static let paste = "doc.on.clipboard"
        static let undo = "arrow.uturn.backward"
        static let redo = "arrow.uturn.forward"
    }
    
    // MARK: - 内容图标
    /// 内容相关图标
    struct Content {
        static let note = "doc.text"
        static let folder = "folder"
        static let folderFill = "folder.fill"
        static let favorite = "heart"
        static let favoriteFill = "heart.fill"
        static let tag = "tag"
        static let tagFill = "tag.fill"
        static let bookmark = "bookmark"
        static let bookmarkFill = "bookmark.fill"
    }
    
    // MARK: - 格式化图标
    /// 富文本格式化图标
    struct Format {
        static let bold = "bold"
        static let italic = "italic"
        static let underline = "underline"
        static let strikethrough = "strikethrough"
        static let textColor = "textformat.abc"
        static let textSize = "textformat.size"
        static let alignLeft = "text.alignleft"
        static let alignCenter = "text.aligncenter"
        static let alignRight = "text.alignright"
        static let list = "list.bullet"
        static let numberedList = "list.number"
    }
    
    // MARK: - 用户界面图标
    /// 用户界面相关图标
    struct Interface {
        static let profile = "person.crop.circle"
        static let settings = "gearshape"
        static let info = "info.circle"
        static let help = "questionmark.circle"
        static let notification = "bell"
        static let notificationFill = "bell.fill"
        static let keyboard = "keyboard"
        static let keyboardDown = "keyboard.chevron.compact.down"
    }
    
    // MARK: - 状态图标
    /// 状态相关图标
    struct Status {
        static let success = "checkmark.circle.fill"
        static let warning = "exclamationmark.triangle.fill"
        static let error = "xmark.circle.fill"
        static let loading = "arrow.2.circlepath"
        static let sync = "arrow.triangle.2.circlepath"
        static let offline = "wifi.slash"
        static let online = "wifi"
    }
    
    // MARK: - 媒体图标
    /// 媒体控制图标
    struct Media {
        static let play = "play.fill"
        static let pause = "pause.fill"
        static let stop = "stop.fill"
        static let record = "record.circle"
        static let microphone = "mic"
        static let microphoneSlash = "mic.slash"
        static let speaker = "speaker.wave.2"
        static let speakerSlash = "speaker.slash"
    }
    
    // MARK: - 工具图标
    /// 工具相关图标
    struct Tool {
        static let teleprompter = "tv"
        static let timer = "timer"
        static let stopwatch = "stopwatch"
        static let ruler = "ruler"
        static let eyedropper = "eyedropper"
        static let paintbrush = "paintbrush"
        static let wand = "wand.and.stars"
    }
}

// MARK: - 图标视图扩展
extension Image {
    /// 创建应用图标
    /// - Parameters:
    ///   - icon: 图标名称
    ///   - size: 图标大小
    ///   - weight: 图标粗细
    /// - Returns: 配置好的图标视图
    static func appIcon(
        _ icon: String,
        size: CGFloat = 16,
        weight: Font.Weight = .medium
    ) -> Image {
        Image(systemName: icon)
    }
}

// MARK: - 图标按钮组件
struct IconButton: View {
    let icon: String
    let size: CGFloat
    let weight: Font.Weight
    let color: Color
    let action: () -> Void
    
    init(
        icon: String,
        size: CGFloat = 16,
        weight: Font.Weight = .medium,
        color: Color = .primary,
        action: @escaping () -> Void
    ) {
        self.icon = icon
        self.size = size
        self.weight = weight
        self.color = color
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: size, weight: weight))
                .foregroundStyle(color)
        }
    }
}

// MARK: - 使用示例和文档
/*
 使用示例：

 // 基础图标使用
 Image(systemName: AppIcons.Navigation.back)
 Image(systemName: AppIcons.Action.add)
 Image(systemName: AppIcons.Content.favorite)

 // 使用扩展方法
 Image.appIcon(AppIcons.Format.bold, size: 18, weight: .semibold)

 // 图标按钮组件
 IconButton(
     icon: AppIcons.Action.save,
     size: 20,
     weight: .semibold,
     color: .accent
 ) {
     // 保存操作
 }

 设计原则：
 1. 使用系统提供的SF Symbols图标
 2. 保持图标语义的一致性
 3. 根据功能重要性选择合适的图标粗细
 4. 确保图标在不同尺寸下的可读性
 5. 遵循Apple Human Interface Guidelines

 维护建议：
 1. 新增图标时按功能分类
 2. 保持命名的一致性和可读性
 3. 定期检查图标的使用情况
 4. 与设计团队保持图标规范同步
 5. 考虑图标的可访问性要求
 */
