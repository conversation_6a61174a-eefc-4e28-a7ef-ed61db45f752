//
//  AppMenuManager.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/19.
//

import SwiftUI

// MARK: - 应用菜单管理器
/// 统一管理应用中所有菜单的创建和配置
/// 提供点击按钮弹出菜单和长按item弹出上下文菜单的完整解决方案

class AppMenuManager {
    
    // MARK: - 单例
    static let shared = AppMenuManager()
    private init() {}
}

// MARK: - 按钮菜单扩展
extension View {
    
    /// 为按钮添加下拉菜单
    /// 使用方法：
    /// ```swift
    /// Button("更多") { }
    ///     .addDropdownMenu {
    ///         AppMenuManager.createMenuItem("设置", icon: "gearshape") {
    ///             // 设置操作
    ///         }
    ///         AppMenuManager.createMenuItem("帮助", icon: "questionmark.circle") {
    ///             // 帮助操作
    ///         }
    ///     }
    /// ```
    func addDropdownMenu<Content: View>(
        @ViewBuilder menuContent: () -> Content
    ) -> some View {
        Menu {
            menuContent()
        } label: {
            self
        }
        .menuStyle(ButtonMenuStyle())
    }
    
    /// 为列表项添加长按上下文菜单
    /// 使用方法：
    /// ```swift
    /// NoteItemView(note: note)
    ///     .addContextMenu {
    ///         AppMenuManager.createMenuItem("编辑", icon: "pencil") {
    ///             // 编辑操作
    ///         }
    ///         AppMenuManager.createMenuItem("删除", icon: "trash", isDestructive: true) {
    ///             // 删除操作
    ///         }
    ///     }
    /// ```
    func addContextMenu<Content: View>(
        @ViewBuilder menuContent: () -> Content
    ) -> some View {
        self.contextMenu {
            menuContent()
        }
    }
    
    /// 为列表项添加带预览的长按上下文菜单
    /// 使用方法：
    /// ```swift
    /// NoteItemView(note: note)
    ///     .addContextMenuWithPreview {
    ///         // 菜单内容
    ///         AppMenuManager.createMenuItem("编辑", icon: "pencil") { }
    ///     } preview: {
    ///         // 预览视图
    ///         NotePreviewView(note: note)
    ///     }
    /// ```
    func addContextMenuWithPreview<MenuContent: View, PreviewContent: View>(
        @ViewBuilder menuContent: () -> MenuContent,
        @ViewBuilder preview: () -> PreviewContent
    ) -> some View {
        self.contextMenu {
            menuContent()
        } preview: {
            preview()
        }
    }
}

// MARK: - 菜单项创建器
extension AppMenuManager {
    
    /// 创建标准菜单项
    /// - Parameters:
    ///   - title: 菜单项标题
    ///   - icon: SF Symbol 图标名称
    ///   - isDestructive: 是否为危险操作（红色显示）
    ///   - action: 点击时执行的操作
    /// - Returns: 菜单项视图
    static func createMenuItem(
        _ title: String,
        icon: String?,
        isDestructive: Bool = false,
        action: @escaping () -> Void
    ) -> some View {
        Button(role: isDestructive ? .destructive : nil) {
            // 自动添加触觉反馈
            if isDestructive {
                AppHaptic.delete()
            } else {
                AppHaptic.selection()
            }
            action()
        } label: {
            if let icon = icon {
                Label {
                    Text(title)
                        .foregroundStyle(.appLabelPrimary)
                        .font(.appLabel)
                } icon: {
                    Image(systemName: icon)
                        .foregroundStyle(.appLabelPrimary)
                        .font(.appLabel)
                }
            } else {
                Text(title)
                    .foregroundStyle(.appLabelPrimary)
                    .font(.appLabel)
            }
        }
    }

    /// 创建带选中状态的菜单项
    /// - Parameters:
    ///   - title: 菜单项标题
    ///   - icon: SF Symbol 图标名称
    ///   - isSelected: 是否选中（显示左侧勾选图标）
    ///   - action: 点击时执行的操作
    /// - Returns: 菜单项视图
    static func createSelectableMenuItem(
        _ title: String,
        icon: String? = nil,
        isSelected: Bool = false,
        action: @escaping () -> Void
    ) -> some View {
        Button {
            AppHaptic.selection()
            action()
        } label: {
            HStack {
                if let icon = icon {
                    Label {
                        Text(title)
                            .foregroundStyle(.appLabelPrimary)
                            .font(.appLabel)
                    } icon: {
                        Image(systemName: icon)
                            .foregroundStyle(.appLabelPrimary)
                            .font(.appLabel)
                    }
                } else {
                    Text(title)
                        .foregroundStyle(.appLabelPrimary)
                        .font(.appLabel)
                }
                Spacer()
                if isSelected {
                    Image(systemName: "checkmark")
                        .foregroundStyle(.appLabelPrimary)
                        .font(.appLabel)
                }
            }
        }
    }
    
    /// 创建选择器菜单
    /// - Parameters:
    ///   - title: 选择器标题
    ///   - selection: 当前选中的值
    ///   - options: 选项数组
    ///   - optionTitle: 获取选项显示文本的闭包
    ///   - optionIcon: 获取选项图标的闭包
    ///   - action: 选择时执行的操作
    /// - Returns: 选择器菜单视图
    static func createPickerMenu<T: Hashable>(
        selection: T,
        options: [T],
        optionTitle: @escaping (T) -> String,
        optionIcon: @escaping (T) -> String? = { _ in nil },
        action: @escaping (T) -> Void
    ) -> some View {
        Picker(selection: .constant(selection), label: Text("")) {
            ForEach(options, id: \.self) { option in
                if let icon = optionIcon(option) {
                    Label(optionTitle(option), systemImage: icon)
                        .tag(option)
                } else {
                    Text(optionTitle(option))
                        .tag(option)
                }
            }
        }
        .onChange(of: selection, { _, newValue in
            action(newValue)
        })
    }

    /// 创建多行文本菜单项
    /// - Parameters:
    ///   - title: 主标题
    ///   - subtitle: 副标题
    ///   - icon: SF Symbol 图标名称
    ///   - isDestructive: 是否为危险操作
    ///   - action: 点击时执行的操作
    /// - Returns: 菜单项视图
    static func createDetailMenuItem(
        _ title: String,
        subtitle: String,
        icon: String? = nil,
        isDestructive: Bool = false,
        action: @escaping () -> Void
    ) -> some View {
        Button(role: isDestructive ? .destructive : nil) {
            if isDestructive {
                AppHaptic.delete()
            } else {
                AppHaptic.selection()
            }
            action()
        } label: {
            if let icon = icon {
                Label {
                    Text(title + "\n")
                        .foregroundStyle(.appLabelPrimary)
                        .font(.appLabel)
                    + Text(subtitle)
                        .foregroundStyle(.appLabelSecondary)
                        .font(.appFootnote)
                } icon: {
                    Image(systemName: icon)
                        .foregroundStyle(.appLabelPrimary)
                        .font(.appLabel)
                }
            } else {
                Text(title + "\n")
                    .foregroundStyle(.appLabelPrimary)
                    .font(.appLabel)
                + Text(subtitle)
                    .foregroundStyle(.appLabelSecondary)
                    .font(.appFootnote)
            }
        }
        .foregroundStyle(.appSecondary)
    }

    /// 创建切换开关菜单项
    /// - Parameters:
    ///   - title: 菜单项标题
    ///   - icon: SF Symbol 图标名称
    ///   - isOn: 开关状态
    ///   - action: 切换时执行的操作
    /// - Returns: 菜单项视图
    static func createToggleMenuItem(
        _ title: String,
        icon: String? = nil,
        isOn: Bool,
        action: @escaping (Bool) -> Void
    ) -> some View {
        Button {
            AppHaptic.selection()
            action(!isOn)
        } label: {
            HStack {
                if let icon = icon {
                    Label {
                        Text(title)
                            .foregroundStyle(.appLabelPrimary)
                            .font(.appLabel)
                    } icon: {
                        Image(systemName: icon)
                            .foregroundStyle(.appLabelPrimary)
                            .font(.appLabel)
                    }

                } else {
                    Text(title)
                        .foregroundStyle(.appLabelPrimary)
                        .font(.appLabel)
                }
                Spacer()
                Image(systemName: isOn ? "checkmark.circle.fill" : "circle")
                    .foregroundStyle(isOn ? .accent : .secondary)
            }
        }
    }
    
    /// 创建切换状态菜单项（如收藏/取消收藏）
    /// - Parameters:
    ///   - title: 菜单项标题
    ///   - icon: SF Symbol 图标名称
    ///   - isOn: 当前状态
    ///   - action: 切换时执行的操作
    /// - Returns: 菜单项视图
    static func createToggleMenuItem(
        _ title: String,
        icon: String,
        isOn: Bool,
        action: @escaping () -> Void
    ) -> some View {
        Button {
            AppHaptic.selection()
            action()
        } label: {
            Label(title, systemImage: icon)
        }
    }
    
    /// 创建子菜单项
    /// - Parameters:
    ///   - title: 子菜单标题
    ///   - icon: SF Symbol 图标名称
    ///   - content: 子菜单内容
    /// - Returns: 子菜单视图
    static func createSubMenu<Content: View>(
        _ title: String,
        icon: String,
        @ViewBuilder content: () -> Content
    ) -> some View {
        Menu {
            content()
        } label: {
            Label(title, systemImage: icon)
        }
    }

    /// 创建菜单分隔线
    /// 使用方法：
    /// ```swift
    /// AppMenuManager.createMenuItem("编辑", icon: "pencil") { }
    /// AppMenuManager.createDivider()
    /// AppMenuManager.createMenuItem("删除", icon: "trash", isDestructive: true) { }
    /// ```
    static func createDivider() -> some View {
        Divider()
    }
}

extension AppMenuManager {
    // 在AppMenuManager中添加通用Alert方法
    static func showAlert(
        title: String,
        message: String? = nil,
        style: UIAlertController.Style = .alert,
        actions: [AlertAction]
    ) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: style)
        
        // 添加所有操作
        for action in actions {
            let uiAction = UIAlertAction(title: action.title, style: action.style) { _ in
                action.handler?()
            }
            alert.addAction(uiAction)
        }
        
        // 获取当前的ViewController
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let rootViewController = window.rootViewController else {
            return
        }
        
        // iPad兼容性处理
        if let popover = alert.popoverPresentationController {
            popover.sourceView = window
            popover.sourceRect = CGRect(x: window.bounds.midX, y: window.bounds.midY, width: 0, height: 0)
            popover.permittedArrowDirections = []
        }
        
        rootViewController.present(alert, animated: true)
    }

    // Alert操作结构体
    struct AlertAction {
        let title: String
        let style: UIAlertAction.Style
        let handler: (() -> Void)?
        
        // 便捷初始化方法
        static func cancel(_ title: String = "Cancel") -> AlertAction {
            AlertAction(title: title, style: .cancel, handler: nil)
        }
        
        static func destructive(_ title: String, handler: @escaping () -> Void) -> AlertAction {
            AlertAction(title: title, style: .destructive, handler: handler)
        }
        
        static func `default`(_ title: String, handler: @escaping () -> Void) -> AlertAction {
            AlertAction(title: title, style: .default, handler: handler)
        }
    }

    // 便捷方法
    static func showConfirmation(
        title: String,
        message: String,
        confirmTitle: String,
        cancelTitle: String,
        isDestructive: Bool,
        onConfirm: @escaping () -> Void
    ) {
        let confirmStyle: UIAlertAction.Style = isDestructive ? .destructive : .default
        showAlert(
            title: title,
            message: message,
            actions: [
                .cancel(cancelTitle),
                AlertAction(title: confirmTitle, style: confirmStyle, handler: onConfirm)
            ]
        )
    }
    
    // 便捷方法
    static func showActionSheetConfirmation(
        title: String,
        message: String,
        confirmTitle: String,
        cancelTitle: String,
        isDestructive: Bool,
        onConfirm: @escaping () -> Void
    ) {
        let confirmStyle: UIAlertAction.Style = isDestructive ? .destructive : .default
        showAlert(
            title: title,
            message: message,
            style: .actionSheet,
            actions: [
                .cancel(cancelTitle),
                AlertAction(title: confirmTitle, style: confirmStyle, handler: onConfirm)
            ]
        )
    }

    
    // 在AppMenuManager中添加Sheet支持
    static func showSheet<Content: View>(
        @ViewBuilder content: () -> Content
    ) {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let rootViewController = window.rootViewController else {
            return
        }
        
        let hostingController = UIHostingController(rootView: content())
        
        // iPad兼容性处理
        if UIDevice.current.userInterfaceIdiom == .pad {
            hostingController.modalPresentationStyle = .formSheet
            hostingController.preferredContentSize = CGSize(width: 400, height: 600)
        } else {
            hostingController.modalPresentationStyle = .pageSheet
        }
        
        rootViewController.present(hostingController, animated: true)
    }

    // 便捷的选项Sheet方法
    static func showOptionsSheet(
        title: String,
        options: [SheetOption],
        onDismiss: (() -> Void)? = nil
    ) {
        showSheet {
            OptionsSheetView(title: title, options: options, onDismiss: onDismiss)
        }
    }

    // Sheet选项结构体
    struct SheetOption {
        let title: String
        let icon: String?
        let isDestructive: Bool
        let handler: () -> Void
        
        static func option(_ title: String, icon: String? = nil, handler: @escaping () -> Void) -> SheetOption {
            SheetOption(title: title, icon: icon, isDestructive: false, handler: handler)
        }
        
        static func destructive(_ title: String, icon: String? = nil, handler: @escaping () -> Void) -> SheetOption {
            SheetOption(title: title, icon: icon, isDestructive: true, handler: handler)
        }
    }

    // Sheet视图组件
    struct OptionsSheetView: View {
        let title: String
        let options: [SheetOption]
        let onDismiss: (() -> Void)?
        @Environment(\.dismiss) private var dismiss
        
        var body: some View {
            NavigationView {
                VStack(spacing: 0) {
                    ForEach(Array(options.enumerated()), id: \.offset) { index, option in
                        Button(action: {
                            option.handler()
                            dismiss()
                        }) {
                            HStack(spacing: 16) {
                                if let icon = option.icon {
                                    Image(systemName: icon)
                                        .font(.system(size: 18, weight: .medium))
                                        .foregroundStyle(option.isDestructive ? .red : .primary)
                                        .frame(width: 24, alignment: .center)
                                }
                                
                                Text(option.title)
                                    .font(.body)
                                    .foregroundStyle(option.isDestructive ? .red : .primary)
                                
                                Spacer()
                            }
                            .padding(.horizontal, 20)
                            .padding(.vertical, 16)
                            .contentShape(Rectangle())
                        }
                        .buttonStyle(PlainButtonStyle())
                        
                        if index < options.count - 1 {
                            Divider()
                                .padding(.horizontal, 20)
                        }
                    }
                    
                    Spacer()
                }
                .navigationTitle(title)
                .navigationBarTitleDisplayMode(.inline)
                .navigationBarItems(
                    trailing: Button("Cancel") {
                        dismiss()
                        onDismiss?()
                    }
                )
            }
            .presentationDetents([.medium, .large])
            .presentationDragIndicator(.visible)
        }
    }
}

// MARK: - 使用示例
/*
 完整使用示例：

 1. 为按钮添加下拉菜单：
 ```swift
 Button {
     // 按钮默认点击操作
 } label: {
     Image(systemName: "ellipsis.circle")
 }
 .addDropdownMenu {
     AppMenuManager.createMenuItem("排序", icon: "arrow.up.arrow.down") {
         // 排序操作
     }
     AppMenuManager.createMenuItem("筛选", icon: "line.3.horizontal.decrease.circle") {
         // 筛选操作
     }
     AppMenuManager.createDivider()
     AppMenuManager.createMenuItem("设置", icon: "gearshape") {
         // 设置操作
     }
 }
 ```

 2. 为列表项添加长按菜单：
 ```swift
 VStack {
     Text(note.title)
     Text(note.content)
 }
 .addContextMenu {
     AppMenuManager.createNoteMenu(
         note: note,
         onEdit: {
             // 编辑笔记
         },
         onShare: {
             // 分享笔记
         },
         onDelete: {
             // 删除笔记
         }
     )
 }
 ```

 3. 为列表项添加带预览的长按菜单：
 ```swift
 NoteItemView(note: note)
     .addContextMenuWithPreview {
         AppMenuManager.createNoteMenu(note: note, onEdit: {}, onShare: {}, onDelete: {})
     } preview: {
         VStack {
             Text(note.title).font(.headline)
             Text(note.content).font(.body)
         }
         .padding()
         .background(.regularMaterial)
         .clipShape(RoundedRectangle(cornerRadius: 12))
     }
 ```

 4. 创建自定义菜单：
 ```swift
 Button("自定义菜单") { }
     .addDropdownMenu {
         AppMenuManager.createMenuItem("操作1", icon: "star") {
             // 操作1
         }
         AppMenuManager.createSubMenu("更多操作", icon: "ellipsis") {
             AppMenuManager.createMenuItem("子操作1", icon: "1.circle") { }
             AppMenuManager.createMenuItem("子操作2", icon: "2.circle") { }
         }
         AppMenuManager.createDivider()
         AppMenuManager.createMenuItem("危险操作", icon: "trash", isDestructive: true) {
             // 危险操作
         }
     }
 ```

 注意事项：
 - 所有菜单项都会自动添加适当的触觉反馈
 - 危险操作会显示为红色并使用删除触觉反馈
 - 菜单会自动遵循系统的外观设置
 - 支持深色模式和浅色模式
 - 菜单项会自动适配可访问性设置
 */
