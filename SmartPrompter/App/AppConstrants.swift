//
//  AppConstrants.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/19.
//

import Foundation
import SwiftUI

// MARK: - App布局管理
/// 应用程序布局常量管理
/// 统一管理间距、字体、圆角、阴影等UI常量，确保设计一致性
struct AppConstrants {

    // MARK: - 间距系统 (Spacing System)
    /// 基于8pt网格系统的间距定义
    struct Spacing {
        /// 极小间距 - 2pt (用于紧密排列的元素)
        static let xxs: CGFloat = 2

        /// 超小间距 - 4pt (用于相关元素之间)
        static let xs: CGFloat = 4

        /// 小间距 - 8pt (用于组件内部元素)
        static let s: CGFloat = 8

        /// 中等间距 - 12pt (用于相关组件之间)
        static let m: CGFloat = 12

        /// 大间距 - 16pt (用于不同区域之间)
        static let l: CGFloat = 16

        /// 超大间距 - 24pt (用于主要区域分隔)
        static let xl: CGFloat = 24

        /// 极大间距 - 32pt (用于页面级别分隔)
        static let xxl: CGFloat = 32

        /// 巨大间距 - 48pt (用于重要内容分隔)
        static let xxxl: CGFloat = 48
    }

    // MARK: - 字体系统 (Typography System)
    /// 应用字体大小定义，与RichTextUtils保持一致
    struct FontSize {
        /// 超大标题 - 28pt
        static let largeTitle: CGFloat = 28

        /// 标题1 - 24pt (与RichTextUtils.TextStyle.title一致)
        static let title1: CGFloat = 24

        /// 标题2 - 20pt (与RichTextUtils.TextStyle.subtitle一致)
        static let title2: CGFloat = 20

        /// 标题3 - 18pt
        static let title3: CGFloat = 18

        /// 正文 - 17pt (与RichTextUtils.TextStyle.body一致)
        static let body: CGFloat = 17

        /// 副标题 - 15pt
        static let subheadline: CGFloat = 15

        /// 说明文字 - 14pt (与RichTextUtils.TextStyle.caption一致)
        static let caption: CGFloat = 14

        /// 小号文字 - 12pt
        static let footnote: CGFloat = 12

        /// 极小文字 - 10pt
        static let caption2: CGFloat = 10
    }

    // MARK: - 圆角系统 (Corner Radius System)
    /// 统一的圆角定义
    struct CornerRadius {
        /// 无圆角
        static let none: CGFloat = 0

        /// 小圆角 - 4pt (用于按钮、输入框)
        static let s: CGFloat = 4

        /// 中等圆角 - 8pt (用于卡片、面板)
        static let m: CGFloat = 8

        /// 大圆角 - 12pt (用于模态框、弹窗)
        static let l: CGFloat = 12

        /// 超大圆角 - 16pt (用于特殊组件)
        static let xl: CGFloat = 16

        /// 圆形 - 50% (用于头像、图标按钮)
        static let circle: CGFloat = 999
    }

    // MARK: - 阴影系统 (Shadow System)
    /// 统一的阴影定义
    struct Shadow {
        /// 无阴影
        static let none = ShadowStyle(color: .clear, radius: 0, x: 0, y: 0)

        /// 轻微阴影 (用于悬浮按钮)
        static let light = ShadowStyle(
            color: Color.black.opacity(0.03),
            radius: 2,
            x: 0,
            y: 3
        )

        /// 中等阴影 (用于卡片)
        static let medium = ShadowStyle(
            color: Color.black.opacity(0.15),
            radius: 4,
            x: 0,
            y: 2
        )

        /// 重阴影 (用于模态框)
        static let heavy = ShadowStyle(
            color: Color.black.opacity(0.2),
            radius: 8,
            x: 0,
            y: 4
        )

        /// 极重阴影 (用于重要弹窗)
        static let extraHeavy = ShadowStyle(
            color: Color.black.opacity(0.25),
            radius: 16,
            x: 0,
            y: 0
        )
    }

    // MARK: - 边框系统 (Border System)
    /// 统一的边框定义
    struct Border {
        /// 细边框 - 0.5pt
        static let thin: CGFloat = 0.5

        /// 标准边框 - 1pt
        static let standard: CGFloat = 1

        /// 粗边框 - 2pt
        static let thick: CGFloat = 2

        /// 超粗边框 - 3pt
        static let extraThick: CGFloat = 3
    }

    // MARK: - 尺寸系统 (Size System)
    /// 常用组件尺寸定义
    struct Size {
        /// 图标尺寸
        struct Icon {
            static let xs: CGFloat = 8

            /// 小图标 - 16pt
            static let s: CGFloat = 16

            /// 中等图标 - 24pt
            static let m: CGFloat = 18

            /// 大图标 - 32pt
            static let l: CGFloat = 24

            /// 超大图标 - 48pt
            static let xl: CGFloat = 32
        }

        /// 按钮尺寸
        struct Button {
            /// 小按钮高度 - 32pt
            static let heightXS: CGFloat = 26

            /// 小按钮高度 - 32pt
            static let heightS: CGFloat = 32

            /// 中等按钮高度 - 44pt (推荐触摸目标)
            static let heightM: CGFloat = 44

            /// 大按钮高度 - 56pt
            static let heightL: CGFloat = 56

            /// 最小触摸区域 - 44pt (Apple HIG推荐)
            static let minTouchArea: CGFloat = 44
        }

        /// 输入框尺寸
        struct TextField {
            /// 标准输入框高度 - 44pt
            static let height: CGFloat = 44

            /// 多行输入框最小高度 - 88pt
            static let multilineMinHeight: CGFloat = 88
        }

        /// 列表项尺寸
        struct ListItem {
            /// 标准列表项高度 - 56pt
            static let height: CGFloat = 56

            /// 紧凑列表项高度 - 44pt
            static let compactHeight: CGFloat = 44

            /// 扩展列表项高度 - 72pt
            static let expandedHeight: CGFloat = 72
        }
    }

    // MARK: - 动画系统 (Animation System)
    /// 统一的动画定义
    struct Animation {
        /// 极快动画 - 0.1秒 (用于按钮反馈)
        static let ultraFast = SwiftUI.Animation.easeInOut(duration: 0.1)

        /// 快速动画 - 0.2秒 (用于状态切换)
        static let fast = SwiftUI.Animation.easeInOut(duration: 0.2)

        /// 标准动画 - 0.3秒 (用于界面过渡)
        static let standard = SwiftUI.Animation.easeInOut(duration: 0.3)

        /// 慢速动画 - 0.5秒 (用于复杂过渡)
        static let slow = SwiftUI.Animation.easeInOut(duration: 0.5)

        /// 弹性动画 (用于弹出效果)
        static let spring = SwiftUI.Animation.spring(
            response: 0.5,
            dampingFraction: 0.8,
            blendDuration: 0
        )

        /// 弹跳动画 (用于强调效果)
        static let bounce = SwiftUI.Animation.spring(
            response: 0.6,
            dampingFraction: 0.6,
            blendDuration: 0
        )

        /// 平滑弹性动画 (用于工具栏显示)
        static let smoothSpring = SwiftUI.Animation.spring(
            response: 0.4,
            dampingFraction: 0.9,
            blendDuration: 0
        )

        /// 微弹性动画 (用于细微交互)
        static let microSpring = SwiftUI.Animation.spring(
            response: 0.3,
            dampingFraction: 0.7,
            blendDuration: 0
        )

        /// 缓入缓出动画 (用于页面切换)
        static let easeInOut = SwiftUI.Animation.timingCurve(0.4, 0, 0.2, 1, duration: 0.3)

        /// 缓入动画 (用于出现效果)
        static let easeIn = SwiftUI.Animation.timingCurve(0.4, 0, 1, 1, duration: 0.3)

        /// 缓出动画 (用于消失效果)
        static let easeOut = SwiftUI.Animation.timingCurve(0, 0, 0.2, 1, duration: 0.3)
    }

    // MARK: - 边框系统 (Border System)
    /// 统一的边框定义
    struct AppBorder {
        /// 极细边框 - 0.5pt
        static let ultraThin: CGFloat = 0.5

        /// 细边框 - 1pt
        static let thin: CGFloat = 1.0

        /// 中等边框 - 1.5pt
        static let medium: CGFloat = 1.5

        /// 粗边框 - 2pt
        static let thick: CGFloat = 2.0

        /// 极粗边框 - 3pt
        static let ultraThick: CGFloat = 3.0
    }

    // MARK: - 透明度系统 (Opacity System)
    /// 统一的透明度定义
    struct Opacity {
        /// 完全透明
        static let transparent: Double = 0.0

        /// 轻微透明 - 5%
        static let light: Double = 0.05

        /// 中等透明 - 10%
        static let medium: Double = 0.1

        /// 重透明 - 20%
        static let heavy: Double = 0.2

        /// 禁用状态 - 40%
        static let disabled: Double = 0.4

        /// 半透明 - 50%
        static let half: Double = 0.5

        /// 完全不透明
        static let opaque: Double = 1.0
    }

    // MARK: - 层级系统 (Z-Index System)
    /// 视图层级定义
    struct ZIndex {
        /// 背景层
        static let background: Double = -1

        /// 默认层
        static let `default`: Double = 0

        /// 内容层
        static let content: Double = 1

        /// 悬浮层 (如工具栏)
        static let floating: Double = 10

        /// 弹窗层
        static let modal: Double = 100

        /// 提示层 (如Toast)
        static let toast: Double = 1000

        /// 最顶层 (如加载指示器)
        static let top: Double = 9999
    }

    // MARK: - 兼容性支持 (Legacy Support)
    /// 为了向后兼容，保留原有的常量名称
    @available(*, deprecated, message: "请使用 AppConstrants.Spacing.xs 替代")
    static let spacingS = Spacing.xs
}

// MARK: - 阴影样式辅助结构
/// 阴影样式定义结构
struct ShadowStyle {
    let color: Color
    let radius: CGFloat
    let x: CGFloat
    let y: CGFloat

    init(color: Color, radius: CGFloat, x: CGFloat, y: CGFloat) {
        self.color = color
        self.radius = radius
        self.x = x
        self.y = y
    }
}

// MARK: - SwiftUI 扩展
/// 为 View 添加便捷的阴影方法
extension View {
    /// 应用预定义的阴影样式
    /// - Parameter style: 阴影样式
    /// - Returns: 应用阴影后的视图
    func shadow(_ style: ShadowStyle) -> some View {
        self.shadow(
            color: style.color,
            radius: style.radius,
            x: style.x,
            y: style.y
        )
    }
}

// MARK: - Font 扩展
/// 为 Font 添加应用专用字体样式
extension Font {
    /// 超大标题字体 - 28pt, bold
    static let appLargeTitle = Font.system(size: AppConstrants.FontSize.largeTitle, weight: .bold)

    /// 标题1字体 - 24pt, bold (与RichTextUtils.TextStyle.title一致)
    static let appTitle1 = Font.system(size: AppConstrants.FontSize.title1, weight: .bold)

    /// 标题2字体 - 20pt, bold (与RichTextUtils.TextStyle.subtitle一致)
    static let appTitle2 = Font.system(size: AppConstrants.FontSize.title2, weight: .bold)

    /// 标题3字体 - 18pt, semibold
    static let appTitle3 = Font.system(size: AppConstrants.FontSize.title3, weight: .semibold)

    /// 正文字体 - 17pt, regular (与RichTextUtils.TextStyle.body一致)
    static let appBody = Font.system(size: AppConstrants.FontSize.body, weight: .regular)

    /// 副标题字体 - 15pt, regular
    static let appSubheadline = Font.system(size: AppConstrants.FontSize.subheadline, weight: .regular)

    /// 说明文字字体 - 14pt, regular (与RichTextUtils.TextStyle.caption一致)
    static let appCaption = Font.system(size: AppConstrants.FontSize.caption, weight: .regular)

    /// 小号文字字体 - 12pt, regular
    static let appFootnote = Font.system(size: AppConstrants.FontSize.footnote, weight: .regular)

    /// 极小文字字体 - 10pt, regular
    static let appCaption2 = Font.system(size: AppConstrants.FontSize.caption2, weight: .regular)

    // MARK: - 特殊用途字体
    /// 按钮字体 - 17pt, medium
    static let appButton = Font.system(size: AppConstrants.FontSize.body, weight: .medium)

    /// 导航标题字体 - 17pt, semibold
    static let appNavTitle = Font.system(size: AppConstrants.FontSize.body, weight: .semibold)

    /// 标签字体 - 14pt, medium
    static let appLabel = Font.system(size: AppConstrants.FontSize.caption, weight: .medium)

    /// 数字字体 - 17pt, monospacedDigit (用于数字显示，保持对齐)
    static let appNumber = Font.system(size: AppConstrants.FontSize.body, weight: .regular, design: .monospaced)
}

// MARK: - 计算属性：获取屏幕相关数据
extension AppConstrants {
    static var safeAreaTop: CGFloat {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return 0
        }
        return window.safeAreaInsets.top
    }
    
    static var safeAreaBot: CGFloat {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return 0
        }
        return window.safeAreaInsets.bottom
    }
}

// MARK: - 使用示例和文档
/*
 使用示例：

 // 间距使用
 VStack(spacing: AppConstrants.Spacing.m) {
     Text("标题")
     Text("内容")
 }
 .padding(AppConstrants.Spacing.l)

 // 字体使用 - 新的便捷语法
 Text("大标题")
     .font(.appTitle1)

 Text("正文内容")
     .font(.appBody)

 Text("说明文字")
     .font(.appCaption)

 // 或者使用原始方式
 Text("标题")
     .font(.system(size: AppConstrants.FontSize.title1, weight: .bold))

 // 圆角使用
 RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.m)

 // 阴影使用
 Rectangle()
     .shadow(AppConstrants.Shadow.medium)

 // 动画使用
 withAnimation(AppConstrants.Animation.spring) {
     // 动画代码
 }

 // 尺寸使用
 Button("按钮") { }
     .frame(height: AppConstrants.Size.Button.heightM)

 // 透明度使用
 Color.black.opacity(AppConstrants.Opacity.medium)

 设计原则：
 1. 基于8pt网格系统，确保像素对齐
 2. 遵循Apple Human Interface Guidelines
 3. 保持视觉层次清晰
 4. 支持深色模式和浅色模式
 5. 考虑可访问性要求

 维护建议：
 1. 新增常量时保持命名一致性
 2. 废弃常量时使用@available标记
 3. 定期检查使用情况，清理无用常量
 4. 与设计团队保持同步
 */
