//
//  AppHaptic.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/19.
//

import Foundation
import UIKit

// MARK: - 触碰反馈管理
/// 应用程序触觉反馈管理器
/// 提供统一的触觉反馈接口，支持不同类型的反馈效果
struct AppHaptic {

    // MARK: - 反馈类型枚举

    /// 触觉反馈类型
    enum FeedbackType {
        /// 轻微反馈 - 用于轻微的交互，如按钮点击
        case light
        /// 中等反馈 - 用于一般的交互，如选择项目
        case medium
        /// 重反馈 - 用于重要的交互，如删除操作
        case heavy
        /// 选择反馈 - 用于选择器或滑动选择
        case selection
        /// 成功反馈 - 用于成功操作
        case success
        /// 警告反馈 - 用于警告操作
        case warning
        /// 错误反馈 - 用于错误操作
        case error
        /// 刚性反馈 - 用于到达边界或限制
        case rigid
        /// 软反馈 - 用于柔和的交互
        case soft
    }

    // MARK: - 私有属性

    /// 轻微冲击反馈生成器
    private static let lightImpactGenerator = UIImpactFeedbackGenerator(style: .light)

    /// 中等冲击反馈生成器
    private static let mediumImpactGenerator = UIImpactFeedbackGenerator(style: .medium)

    /// 重冲击反馈生成器
    private static let heavyImpactGenerator = UIImpactFeedbackGenerator(style: .heavy)

    /// 选择反馈生成器
    private static let selectionGenerator = UISelectionFeedbackGenerator()

    /// 通知反馈生成器
    private static let notificationGenerator = UINotificationFeedbackGenerator()

    /// 刚性冲击反馈生成器 (iOS 13+)
    @available(iOS 13.0, *)
    private static let rigidImpactGenerator = UIImpactFeedbackGenerator(style: .rigid)

    /// 软冲击反馈生成器 (iOS 13+)
    @available(iOS 13.0, *)
    private static let softImpactGenerator = UIImpactFeedbackGenerator(style: .soft)

    /// 是否启用触觉反馈
    private static var isHapticEnabled: Bool {
        // 检查是否在模拟器中运行
        #if targetEnvironment(simulator)
        return false
        #else
        // 检查设备是否支持触觉反馈
        return UIDevice.current.userInterfaceIdiom == .phone
        #endif
    }

    /// 是否在调试模式下显示触觉反馈日志
    private static let isDebugMode: Bool = {
        #if DEBUG
        return true
        #else
        return false
        #endif
    }()

    /// 安全地执行触觉反馈操作
    /// - Parameter operation: 要执行的触觉反馈操作
    private static func safelyExecute(_ operation: () -> Void) {
        guard isHapticEnabled else {
            if isDebugMode {
                LOG_INFO("🔇 触觉反馈已禁用（模拟器或不支持的设备）")
            }
            return
        }

        operation()
        if isDebugMode {
            LOG_INFO("📳 触觉反馈已触发")
        }
    }

    // MARK: - 公共方法

    /// 触发触觉反馈
    /// - Parameter type: 反馈类型
    static func trigger(_ type: FeedbackType) {
        guard isHapticEnabled else { return }

        switch type {
        case .light:
            lightImpactGenerator.prepare()
            lightImpactGenerator.impactOccurred()

        case .medium:
            mediumImpactGenerator.prepare()
            mediumImpactGenerator.impactOccurred()

        case .heavy:
            heavyImpactGenerator.prepare()
            heavyImpactGenerator.impactOccurred()

        case .selection:
            selectionGenerator.prepare()
            selectionGenerator.selectionChanged()

        case .success:
            notificationGenerator.prepare()
            notificationGenerator.notificationOccurred(.success)

        case .warning:
            notificationGenerator.prepare()
            notificationGenerator.notificationOccurred(.warning)

        case .error:
            notificationGenerator.prepare()
            notificationGenerator.notificationOccurred(.error)

        case .rigid:
            if #available(iOS 13.0, *) {
                rigidImpactGenerator.prepare()
                rigidImpactGenerator.impactOccurred()
            } else {
                // 降级到重反馈
                heavyImpactGenerator.prepare()
                heavyImpactGenerator.impactOccurred()
            }

        case .soft:
            if #available(iOS 13.0, *) {
                softImpactGenerator.prepare()
                softImpactGenerator.impactOccurred()
            } else {
                // 降级到轻反馈
                lightImpactGenerator.prepare()
                lightImpactGenerator.impactOccurred()
            }
        }
    }

    /// 触发自定义强度的冲击反馈
    /// - Parameter intensity: 强度值 (0.0 - 1.0)
    static func triggerImpact(intensity: CGFloat) {
        guard isHapticEnabled else { return }

        let clampedIntensity = max(0.0, min(1.0, intensity))

        if #available(iOS 13.0, *) {
            let generator = UIImpactFeedbackGenerator(style: .medium)
            generator.prepare()
            generator.impactOccurred(intensity: clampedIntensity)
        } else {
            // iOS 13 以下版本不支持自定义强度，使用预设强度
            if clampedIntensity < 0.3 {
                trigger(.light)
            } else if clampedIntensity < 0.7 {
                trigger(.medium)
            } else {
                trigger(.heavy)
            }
        }
    }

    // MARK: - 便捷方法

    /// 按钮点击反馈
    static func buttonTap() {
        trigger(.light)
    }

    /// 选择项目反馈
    static func selection() {
        trigger(.selection)
    }

    /// 成功操作反馈
    static func success() {
        trigger(.success)
    }

    /// 错误操作反馈
    static func error() {
        trigger(.error)
    }

    /// 警告操作反馈
    static func warning() {
        trigger(.warning)
    }

    /// 删除操作反馈
    static func delete() {
        trigger(.heavy)
    }

    /// 刷新操作反馈
    static func refresh() {
        trigger(.medium)
    }

    /// 到达边界反馈
    static func boundary() {
        trigger(.rigid)
    }

    /// 滑动反馈
    static func swipe() {
        trigger(.soft)
    }

    /// 长按反馈
    static func longPress() {
        trigger(.heavy)
    }

    /// 切换开关反馈
    static func toggle() {
        trigger(.medium)
    }

    // MARK: - 复合反馈

    /// 双击反馈
    static func doubleTap() {
        trigger(.light)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            trigger(.light)
        }
    }

    /// 三连击反馈
    static func tripleTap() {
        trigger(.light)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            trigger(.light)
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            trigger(.light)
        }
    }

    /// 渐强反馈序列
    static func crescendo() {
        trigger(.soft)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            trigger(.light)
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            trigger(.medium)
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            trigger(.heavy)
        }
    }

    /// 心跳反馈
    static func heartbeat() {
        trigger(.medium)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            trigger(.medium)
        }
    }

    // MARK: - 预加载方法

    /// 预加载所有反馈生成器
    /// 在应用启动时调用，提高首次反馈的响应速度
    static func prepareAll() {
        guard isHapticEnabled else { return }

        lightImpactGenerator.prepare()
        mediumImpactGenerator.prepare()
        heavyImpactGenerator.prepare()
        selectionGenerator.prepare()
        notificationGenerator.prepare()

        if #available(iOS 13.0, *) {
            rigidImpactGenerator.prepare()
            softImpactGenerator.prepare()
        }
    }

    /// 预加载特定类型的反馈生成器
    /// - Parameter types: 要预加载的反馈类型数组
    static func prepare(_ types: [FeedbackType]) {
        guard isHapticEnabled else { return }

        for type in types {
            switch type {
            case .light:
                lightImpactGenerator.prepare()
            case .medium:
                mediumImpactGenerator.prepare()
            case .heavy:
                heavyImpactGenerator.prepare()
            case .selection:
                selectionGenerator.prepare()
            case .success, .warning, .error:
                notificationGenerator.prepare()
            case .rigid:
                if #available(iOS 13.0, *) {
                    rigidImpactGenerator.prepare()
                }
            case .soft:
                if #available(iOS 13.0, *) {
                    softImpactGenerator.prepare()
                }
            }
        }
    }
}

// MARK: - SwiftUI 扩展
import SwiftUI

/// 为 View 添加触觉反馈的便捷方法
extension View {

    /// 添加点击触觉反馈
    /// - Parameter feedbackType: 反馈类型，默认为轻微反馈
    /// - Returns: 添加了触觉反馈的视图
    func hapticFeedback(_ feedbackType: AppHaptic.FeedbackType = .light) -> some View {
        self.onTapGesture {
            AppHaptic.trigger(feedbackType)
        }
    }

    /// 添加长按触觉反馈
    /// - Parameters:
    ///   - minimumDuration: 最小长按时间，默认0.5秒
    ///   - feedbackType: 反馈类型，默认为重反馈
    ///   - action: 长按时执行的动作
    /// - Returns: 添加了长按触觉反馈的视图
    func hapticLongPress(
        minimumDuration: Double = 0.5,
        feedbackType: AppHaptic.FeedbackType = .heavy,
        action: @escaping () -> Void = {}
    ) -> some View {
        self.onLongPressGesture(minimumDuration: minimumDuration) {
            AppHaptic.trigger(feedbackType)
            action()
        }
    }

    /// 添加拖拽触觉反馈
    /// - Parameter feedbackType: 反馈类型，默认为选择反馈
    /// - Returns: 添加了拖拽触觉反馈的视图
    func hapticDrag(_ feedbackType: AppHaptic.FeedbackType = .selection) -> some View {
        self.gesture(
            DragGesture()
                .onChanged { _ in
                    AppHaptic.trigger(feedbackType)
                }
        )
    }
}

// MARK: - 使用示例和文档
/*
 使用示例：

 // 基础使用
 AppHaptic.trigger(.light)        // 轻微反馈
 AppHaptic.trigger(.success)      // 成功反馈
 AppHaptic.trigger(.error)        // 错误反馈

 // 便捷方法
 AppHaptic.buttonTap()           // 按钮点击
 AppHaptic.success()             // 成功操作
 AppHaptic.delete()              // 删除操作
 AppHaptic.selection()           // 选择操作

 // 复合反馈
 AppHaptic.doubleTap()           // 双击反馈
 AppHaptic.heartbeat()           // 心跳反馈
 AppHaptic.crescendo()           // 渐强反馈

 // 自定义强度 (iOS 13+)
 AppHaptic.triggerImpact(intensity: 0.8)

 // SwiftUI 中使用
 Button("点击我") {
     // 按钮动作
 }
 .hapticFeedback(.medium)

 // 或者使用便捷构造器
 Button.withHaptic(.success, action: {
     // 按钮动作
 }) {
     Text("成功按钮")
 }

 // 长按反馈
 Text("长按我")
     .hapticLongPress(feedbackType: .heavy) {
         // 长按动作
     }

 // 应用启动时预加载
 AppHaptic.prepareAll()

 // 或预加载特定类型
 AppHaptic.prepare([.light, .medium, .success])

 反馈类型指南：
 - .light: 轻微交互，如按钮点击、开关切换
 - .medium: 一般交互，如选择列表项、刷新
 - .heavy: 重要交互，如删除、长按、确认
 - .selection: 选择器、滑动选择、滚动
 - .success: 成功操作，如保存成功、发送成功
 - .warning: 警告操作，如即将删除、权限警告
 - .error: 错误操作，如输入错误、网络错误
 - .rigid: 到达边界、限制操作
 - .soft: 柔和交互，如轻微滑动、悬停

 最佳实践：
 1. 在应用启动时调用 prepareAll() 预加载反馈生成器
 2. 根据交互的重要性选择合适的反馈强度
 3. 避免过度使用，保持用户体验的平衡
 4. 考虑用户的可访问性设置
 5. 在关键操作前预加载相应的反馈生成器
 */
