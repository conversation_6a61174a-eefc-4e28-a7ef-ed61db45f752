//
//  AppLog.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/19.
//

import Foundation
import os.log

// MARK: - 日志级别
enum LogLevel: String {
    case debug = "DEBUG"
    case info = "INFO"
    case warning = "WARNING"
    case error = "ERROR"
    case success = "SUCCESS"
    
    var emoji: String {
        switch self {
        case .debug: return "🐛"
        case .info: return "ℹ️"
        case .warning: return "⚠️"
        case .error: return "❌"
        case .success: return "✅"
        }
    }
}

// MARK: - 静态日志工具
struct Log {
    private static let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss.SSS"
        return formatter
    }()
    
    private static let osLog = OSLog(subsystem: "com.photoeditor.app", category: "general")
    
    // MARK: - 静态方法
    static func debug(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .debug, message: message, file: file, function: function, line: line)
    }
    
    static func info(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .info, message: message, file: file, function: function, line: line)
    }
    
    static func warning(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .warning, message: message, file: file, function: function, line: line)
    }
    
    static func error(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .error, message: message, file: file, function: function, line: line)
    }
    
    static func success(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .success, message: message, file: file, function: function, line: line)
    }
    
    // MARK: - 核心日志方法
    private static func log(level: LogLevel, message: String, file: String, function: String, line: Int) {
        #if DEBUG
        let fileName = (file as NSString).lastPathComponent
        let timeString = dateFormatter.string(from: Date())
        let logMessage = "\(level.emoji) [\(level.rawValue)] \(timeString) \(fileName):\(line) \(function) → \(message)"

        // 控制台输出（开发时查看）
        print(logMessage)
        
        #else
        // Release版本只写系统日志，不打印到控制台
        os_log("%@", log: osLog, type: logType(for: level), message)
        #endif
    }
    
    private static func logType(for level: LogLevel) -> OSLogType {
        switch level {
        case .debug: return .debug
        case .info: return .info
        case .warning: return .default
        case .error: return .error
        case .success: return .info
        }
    }
}

// MARK: - 全局便捷方法
func LOG_DEBUG(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    Log.debug(message, file: file, function: function, line: line)
}

func LOG_INFO(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    Log.info(message, file: file, function: function, line: line)
}

func LOG_WARNING(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    Log.warning(message, file: file, function: function, line: line)
}

func LOG_ERROR(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    Log.error(message, file: file, function: function, line: line)
}

func LOG_SUCCESS(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    Log.success(message, file: file, function: function, line: line)
}
