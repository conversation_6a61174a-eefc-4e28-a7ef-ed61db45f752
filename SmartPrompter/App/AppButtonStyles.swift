//
//  AppButtonStyles.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/19.
//

import SwiftUI

// MARK: - 应用按钮样式系统
/// 统一的按钮样式定义，确保整个应用的按钮交互一致性

// MARK: - 基础按钮样式
/// 缩放按钮样式 - 提供按下时的缩放和透明度反馈
struct ScaleButtonStyle: ButtonStyle {
    let scaleEffect: CGFloat
    let opacityEffect: CGFloat
    let animationDuration: Double

    init(
        scaleEffect: CGFloat = 0.95,
        opacityEffect: CGFloat = 0.8,
        animationDuration: Double = 0.1
    ) {
        self.scaleEffect = scaleEffect
        self.opacityEffect = opacityEffect
        self.animationDuration = animationDuration
    }

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? scaleEffect : 1.0)
            .opacity(configuration.isPressed ? opacityEffect : 1.0)
            .animation(AppConstrants.Animation.microSpring, value: configuration.isPressed)
    }
}

/// 弹性按钮样式 - 提供弹性的按下反馈
struct SpringButtonStyle: ButtonStyle {
    let scaleEffect: CGFloat
    let hapticFeedback: Bool

    init(scaleEffect: CGFloat = 0.92, hapticFeedback: Bool = true) {
        self.scaleEffect = scaleEffect
        self.hapticFeedback = hapticFeedback
    }

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? scaleEffect : 1.0)
            .animation(AppConstrants.Animation.spring, value: configuration.isPressed)
            .onChange(of: configuration.isPressed) { _, isPressed in
                if isPressed && hapticFeedback {
                    AppHaptic.buttonTap()
                }
            }
    }
}

/// 渐变按钮样式 - 提供渐变的按下效果
struct GradientButtonStyle: ButtonStyle {
    let pressedOpacity: CGFloat
    let shadowIntensity: CGFloat

    init(pressedOpacity: CGFloat = 0.7, shadowIntensity: CGFloat = 0.5) {
        self.pressedOpacity = pressedOpacity
        self.shadowIntensity = shadowIntensity
    }

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .opacity(configuration.isPressed ? pressedOpacity : 1.0)
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .shadow(
                color: .black.opacity(configuration.isPressed ? 0.1 : 0.2 * shadowIntensity),
                radius: configuration.isPressed ? 2 : 4,
                x: 0,
                y: configuration.isPressed ? 1 : 2
            )
            .animation(AppConstrants.Animation.easeInOut, value: configuration.isPressed)
    }
}

// MARK: - 专用按钮样式
/// 主要操作按钮样式 - 用于重要的操作按钮
struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.96 : 1.0)
            .opacity(configuration.isPressed ? 0.9 : 1.0)
            .animation(AppConstrants.Animation.microSpring, value: configuration.isPressed)
            .onChange(of: configuration.isPressed) { _, isPressed in
                if isPressed {
                    AppHaptic.success()
                }
            }
    }
}

/// 次要操作按钮样式 - 用于次要的操作按钮
struct SecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.94 : 1.0)
            .opacity(configuration.isPressed ? 0.7 : 1.0)
            .animation(AppConstrants.Animation.ultraFast, value: configuration.isPressed)
            .onChange(of: configuration.isPressed) { _, isPressed in
                if isPressed {
                    AppHaptic.buttonTap()
                }
            }
    }
}

/// 工具栏按钮样式 - 用于工具栏中的按钮
struct ToolbarButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.92 : 1.0)
            .opacity(configuration.isPressed ? 0.6 : 1.0)
            .animation(AppConstrants.Animation.ultraFast, value: configuration.isPressed)
            .onChange(of: configuration.isPressed) { _, isPressed in
                if isPressed {
                    AppHaptic.selection()
                }
            }
    }
}

/// 浮动按钮样式 - 用于悬浮的操作按钮
struct FloatingButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.9 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .shadow(
                color: .black.opacity(configuration.isPressed ? 0.15 : 0.25),
                radius: configuration.isPressed ? 8 : 12,
                x: 0,
                y: configuration.isPressed ? 4 : 6
            )
            .animation(AppConstrants.Animation.bounce, value: configuration.isPressed)
            .onChange(of: configuration.isPressed) { _, isPressed in
                if isPressed {
                    AppHaptic.success()
                }
            }
    }
}

// MARK: - 按钮组件扩展
extension View {
    /// 应用缩放按钮样式
    func scaleButtonStyle(
        scaleEffect: CGFloat = 0.95,
        opacityEffect: CGFloat = 0.8,
        animationDuration: Double = 0.1
    ) -> some View {
        self.buttonStyle(ScaleButtonStyle(
            scaleEffect: scaleEffect,
            opacityEffect: opacityEffect,
            animationDuration: animationDuration
        ))
    }
    
    /// 应用主要按钮样式
    func primaryButtonStyle() -> some View {
        self.buttonStyle(PrimaryButtonStyle())
    }
    
    /// 应用次要按钮样式
    func secondaryButtonStyle() -> some View {
        self.buttonStyle(SecondaryButtonStyle())
    }
    
    /// 应用工具栏按钮样式
    func toolbarButtonStyle() -> some View {
        self.buttonStyle(ToolbarButtonStyle())
    }
    
    /// 应用浮动按钮样式
    func floatingButtonStyle() -> some View {
        self.buttonStyle(FloatingButtonStyle())
    }
}

// MARK: - 按钮构建器
/// 便捷的按钮构建器，提供常用的按钮样式组合
struct AppButton {
    
    /// 主要操作按钮
    static func primary<Label: View>(
        action: @escaping () -> Void,
        @ViewBuilder label: () -> Label
    ) -> some View {
        Button(action: action, label: label)
            .primaryButtonStyle()
    }
    
    /// 次要操作按钮
    static func secondary<Label: View>(
        action: @escaping () -> Void,
        @ViewBuilder label: () -> Label
    ) -> some View {
        Button(action: action, label: label)
            .secondaryButtonStyle()
    }
    
    /// 工具栏按钮
    static func toolbar<Label: View>(
        action: @escaping () -> Void,
        @ViewBuilder label: () -> Label
    ) -> some View {
        Button(action: action, label: label)
            .toolbarButtonStyle()
    }
    
    /// 浮动按钮
    static func floating<Label: View>(
        action: @escaping () -> Void,
        @ViewBuilder label: () -> Label
    ) -> some View {
        Button(action: action, label: label)
            .floatingButtonStyle()
    }
}

extension View {

    public func appCornerRadiusAndBoard(radius: CGFloat, boardColor: Color, lineWidth: CGFloat = 1.0) -> some View {
        return self.clipShape(RoundedRectangle(cornerRadius: radius, style: .continuous))
            .overlay(RoundedRectangle(cornerRadius: radius).strokeBorder(boardColor, lineWidth: lineWidth))
    }
    
    public func appCapsuleAndBoard(boardColor: Color, lineWidth: CGFloat = 1.0) -> some View {
        return self.clipShape(.capsule)
            .overlay(Capsule().strokeBorder(boardColor, lineWidth: lineWidth))
    }
    
    @ViewBuilder
    func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }
}

// MARK: - 使用示例和文档
/*
 使用示例：

 // 基础缩放按钮
 Button("点击我") { }
     .scaleButtonStyle()

 // 自定义缩放效果
 Button("点击我") { }
     .scaleButtonStyle(scaleEffect: 0.9, opacityEffect: 0.7)

 // 使用预定义样式
 Button("主要操作") { }
     .primaryButtonStyle()

 Button("次要操作") { }
     .secondaryButtonStyle()

 // 使用便捷构建器
 AppButton.primary(action: {
     // 主要操作
 }) {
     Text("主要按钮")
 }

 AppButton.toolbar(action: {
     // 工具栏操作
 }) {
     Image(systemName: "plus")
 }

 设计原则：
 1. 提供即时的视觉反馈
 2. 保持动画的一致性
 3. 区分不同重要级别的按钮
 4. 遵循Apple Human Interface Guidelines
 5. 支持可访问性要求

 维护建议：
 1. 新增按钮样式时保持命名一致性
 2. 确保动画时长和缓动函数的协调性
 3. 定期检查按钮样式的使用情况
 4. 与设计团队保持样式规范同步
 */
