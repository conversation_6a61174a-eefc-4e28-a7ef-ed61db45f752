//
//  EditNoteView+TextView.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/19.
//

import SwiftUI

// MARK: - 输入框视图
extension EditNoteView {

    @ViewBuilder
    func editView() -> some View {
        VStack(spacing: AppConstrants.Spacing.l) {
            editTitleView()
            editContentView()
        }
    }

    @ViewBuilder
    func editTitleView() -> some View {
        ZStack(alignment: .topLeading) {
            TextField(text: $title) {
                Text("Enter Title")
                    .foregroundStyle(.appLabelSecondary.opacity(0.6))
                    .font(.system(size: 16, weight: .medium))
            }
            .font(.system(size: 16, weight: .medium))
            .foregroundStyle(.appLabelPrimary)
            .padding(.horizontal, AppConstrants.Spacing.s)
            .onChange(of: title) {
                if title.count > 30 {
                    title = String(title.prefix(30))
                }
            }
        }
        .frame(height: 48)
        .background(
            LinearGradient(
                colors: [.appBackgroundSecondary, .appBackgroundSecondary.opacity(0.8)],
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .clipShape(RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.m))
        .overlay(
            RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.m)
                .stroke(.appLabelSecondary.opacity(0.1), lineWidth: AppConstrants.AppBorder.ultraThin)
        )
        .shadow(AppConstrants.Shadow.light)
        .padding(.horizontal, AppConstrants.Spacing.l)
    }

    @ViewBuilder
    func editContentView() -> some View {
        VStack(spacing: 0) {
            // 富文本编辑器
            ZStack(alignment: .topLeading) {
                RichTextEditor(attributedText: $content, editorState: richTextEditorState)
                    .focused($contentEditFocus)
                    .padding(.top, 6)
                    .padding(.horizontal, 4)

                if content.string.isEmpty {
                    VStack(alignment: .leading, spacing: AppConstrants.Spacing.xs) {
                        Text("Enter Script Text")
                            .foregroundStyle(.appLabelSecondary.opacity(0.6))
                            .font(.system(size: 16, weight: .medium))

                        Text("Start typing your teleprompter script...")
                            .foregroundStyle(.appLabelSecondary.opacity(0.4))
                            .font(.system(size: 14, weight: .regular))
                    }
                    .padding(.top, 14)
                    .padding(.leading, 8)
                    .allowsHitTesting(false)
                }
            }
            .frame(maxHeight: .infinity)
            .background(
                LinearGradient(
                    colors: [.appBackgroundSecondary, .appBackgroundSecondary.opacity(0.8)],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .clipShape(RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.m))
            .overlay(
                RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.m)
                    .stroke(.appLabelSecondary.opacity(0.1), lineWidth: AppConstrants.AppBorder.ultraThin)
            )
            .shadow(AppConstrants.Shadow.light)

            // 富文本工具栏
            if contentEditFocus {
                RichTextToolbar(editorState: richTextEditorState,
                                attributedText: $content)
                    .padding(.top, AppConstrants.Spacing.m)
                    .padding(.bottom, AppConstrants.Spacing.s)
                    .transition(
                        .asymmetric(
                            insertion: .move(edge: .bottom)
                                .combined(with: .opacity)
                                .combined(with: .scale(scale: 0.95)),
                            removal: .move(edge: .bottom)
                                .combined(with: .opacity)
                                .combined(with: .scale(scale: 0.95))
                        )
                    )
            }
        }
        .padding(.horizontal, AppConstrants.Spacing.l)
        .animation(AppConstrants.Animation.smoothSpring, value: contentEditFocus)
    }
}
