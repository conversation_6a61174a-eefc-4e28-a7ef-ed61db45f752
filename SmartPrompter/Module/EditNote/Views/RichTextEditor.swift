//
//  RichTextEditor.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/19.
//

import SwiftUI
import UIKit

// MARK: - 富文本编辑器状态管理
class RichTextEditorState: ObservableObject {
    @Published var canUndo: Bool = false
    @Published var canRedo: Bool = false

    // 当前格式状态
    @Published var isBold: Bool = false
    @Published var isItalic: Bool = false
    @Published var isUnderlined: Bool = false
    @Published var currentTextColor: UIColor = UIColor.label

    weak var textView: UITextView?
    
    // 重置所有格式状态（选中文本时调用）
    func resetFormatStates() {
        isBold = false
        isItalic = false
        isUnderlined = false
    }

    // 更新撤销重做状态
    func updateUndoRedoStates() {
        guard let textView = textView else { return }
        canUndo = textView.undoManager?.canUndo ?? false
        canRedo = textView.undoManager?.canRedo ?? false
    }

    // 更新格式状态（点击格式化按钮后调用）
    func updateFormatState(bold: Bool? = nil, italic: Bool? = nil, underlined: Bool? = nil, color: UIColor? = nil) {
        if let bold = bold {
            isBold = bold
        }
        if let italic = italic {
            isItalic = italic
        }
        if let underlined = underlined {
            isUnderlined = underlined
        }
    }
}

// MARK: - 富文本编辑器
struct RichTextEditor: UIViewRepresentable {
    @Binding var attributedText: NSAttributedString
    @ObservedObject var editorState: RichTextEditorState
    var lastSelectionRange: NSRange?

    init(attributedText: Binding<NSAttributedString>, editorState: RichTextEditorState = RichTextEditorState()) {
        self._attributedText = attributedText
        self.editorState = editorState
    }

    func makeUIView(context: Context) -> UITextView {
        let textView = UITextView()
        textView.delegate = context.coordinator
        textView.font = UIFont.systemFont(ofSize: 17)
        textView.textColor = .label
        textView.backgroundColor = UIColor.clear
        textView.allowsEditingTextAttributes = true
        textView.isEditable = true
        textView.isSelectable = true

        // 关联状态管理器
        editorState.textView = textView

        return textView
    }

    func updateUIView(_ uiView: UITextView, context: Context) {
        if uiView.attributedText != attributedText {
            let selectedRange = uiView.selectedRange
            uiView.attributedText = attributedText

            // 恢复选中范围
            if selectedRange.location <= attributedText.length {
                uiView.selectedRange = selectedRange
            }
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UITextViewDelegate {
        var parent: RichTextEditor
        private var lastTextLength: Int = 0

        init(_ parent: RichTextEditor) {
            self.parent = parent
            super.init()
        }

        func textViewDidChange(_ textView: UITextView) {
            let currentLength = textView.attributedText.length
            let lengthDifference = currentLength - lastTextLength
            
            // 如果文本长度增加了很多（可能是粘贴操作）
            if lengthDifference > 10 {
                let insertionPoint = textView.selectedRange.location - lengthDifference
                if insertionPoint >= 0 {
                    normalizeNewlyPastedText(textView: textView, insertionPoint: insertionPoint, length: lengthDifference)
                }
            }
            
            lastTextLength = currentLength
            parent.attributedText = textView.attributedText
            
            // 文本变化时只更新撤销重做状态
            DispatchQueue.main.async {
                self.parent.editorState.updateUndoRedoStates()
            }
        }

        /// 只标准化新粘贴的文本内容
        private func normalizeNewlyPastedText(textView: UITextView, insertionPoint: Int, length: Int) {
            let textStorage = textView.textStorage
            
            let pastedRange = NSRange(location: insertionPoint, length: length)
            
            // 检查粘贴的内容是否需要标准化
            var needsNormalization = false
            textStorage.enumerateAttributes(in: pastedRange, options: []) { (attributes, range, _) in
                let currentFont = attributes[.font] as? UIFont ?? UIFont.systemFont(ofSize: 17)
                
                if currentFont.pointSize != 17 ||
                   currentFont.fontDescriptor.symbolicTraits.contains(.traitBold) ||
                   currentFont.fontDescriptor.symbolicTraits.contains(.traitItalic) ||
                   attributes[.underlineStyle] != nil ||
                   attributes.count > 2 {
                    needsNormalization = true
                }
            }
            
            // 如果需要标准化，只处理粘贴的部分
            if needsNormalization {
                textStorage.beginEditing()
                
                // 设置标准属性
                textStorage.addAttribute(.font, value: UIFont.systemFont(ofSize: 17), range: pastedRange)
                textStorage.addAttribute(.foregroundColor, value: parent.editorState.currentTextColor, range: pastedRange)
                
                // 移除其他格式属性
                textStorage.removeAttribute(.underlineStyle, range: pastedRange)
                
                textStorage.endEditing()
            }
        }

        /// 创建标准化字体（保持原有的粗体、斜体等属性）
        private func createNormalizedFont(from originalFont: UIFont, targetSize: CGFloat) -> UIFont {
            let traits = originalFont.fontDescriptor.symbolicTraits
            let isBold = traits.contains(.traitBold)
            let isItalic = traits.contains(.traitItalic)

            if isBold && isItalic {
                // 粗体斜体
                let descriptor = UIFont.systemFont(ofSize: targetSize).fontDescriptor
                let boldItalicTraits: UIFontDescriptor.SymbolicTraits = [.traitBold, .traitItalic]
                if let boldItalicDescriptor = descriptor.withSymbolicTraits(boldItalicTraits) {
                    return UIFont(descriptor: boldItalicDescriptor, size: targetSize)
                }
                return UIFont.boldSystemFont(ofSize: targetSize)
            } else if isBold {
                // 粗体
                return UIFont.boldSystemFont(ofSize: targetSize)
            } else if isItalic {
                // 斜体
                return UIFont.italicSystemFont(ofSize: targetSize)
            } else {
                // 普通字体
                return UIFont.systemFont(ofSize: targetSize)
            }
        }

        func textViewDidChangeSelection(_ textView: UITextView) {
            let range = textView.selectedRange
            if let lastSelectionRange = parent.lastSelectionRange,
                lastSelectionRange == range {
                return
            }
            
            // 更新 typingAttributes 以匹配当前光标位置的格式
            updateTypingAttributes(textView: textView, at: range)

            // 如果选中了文本，重置所有格式按钮状态为未选中
            // 这样用户可以对选中的文本应用新的格式
            if range.length > 0 {
                parent.lastSelectionRange = range
                DispatchQueue.main.async {
                    self.parent.editorState.resetFormatStates()
                }
            }
        }
        
        /// 根据光标位置更新 typingAttributes
        private func updateTypingAttributes(textView: UITextView, at range: NSRange) {
            // 只在没有选中文本时更新 typingAttributes
            guard range.length == 0 else { return }
            
            // 根据当前格式状态构建字体
            let editorState = parent.editorState
            let fontSize: CGFloat = 17
            
            let font: UIFont
            if editorState.isBold && editorState.isItalic {
                // 粗体斜体
                let descriptor = UIFont.systemFont(ofSize: fontSize).fontDescriptor
                let boldItalicTraits: UIFontDescriptor.SymbolicTraits = [.traitBold, .traitItalic]
                if let boldItalicDescriptor = descriptor.withSymbolicTraits(boldItalicTraits) {
                    font = UIFont(descriptor: boldItalicDescriptor, size: fontSize)
                } else {
                    font = UIFont.boldSystemFont(ofSize: fontSize)
                }
            } else if editorState.isBold {
                // 只有粗体
                font = UIFont.boldSystemFont(ofSize: fontSize)
            } else if editorState.isItalic {
                // 只有斜体
                font = UIFont.italicSystemFont(ofSize: fontSize)
            } else {
                // 普通字体
                font = UIFont.systemFont(ofSize: fontSize)
            }
            
            // 构建 typingAttributes
            var newTypingAttributes: [NSAttributedString.Key: Any] = [
                .font: font,
                .foregroundColor: editorState.currentTextColor
            ]
            
            // 添加下划线（如果需要）
            if editorState.isUnderlined {
                newTypingAttributes[.underlineStyle] = NSUnderlineStyle.single.rawValue
            }
            
            textView.typingAttributes = newTypingAttributes
        }

        func textViewDidBeginEditing(_ textView: UITextView) {
            // 开始编辑时更新撤销重做状态
            DispatchQueue.main.async {
                self.parent.editorState.updateUndoRedoStates()
            }
        }

        func textViewDidEndEditing(_ textView: UITextView) {
            // 结束编辑时更新撤销重做状态
            DispatchQueue.main.async {
                self.parent.editorState.updateUndoRedoStates()
            }
        }
    }
}

// MARK: - 辅助方法
extension RichTextEditor {
    /// 获取当前属性
    static func getCurrentAttributes(textView: UITextView) -> [NSAttributedString.Key: Any] {
        let range = textView.selectedRange
        if range.length > 0 {
            return textView.attributedText.attributes(at: range.location, effectiveRange: nil)
        } else {
            return textView.typingAttributes
        }
    }

    /// 检查当前是否为粗体
    static func getCurrentBoldState(textView: UITextView) -> Bool {
        let attributes = getCurrentAttributes(textView: textView)
        if let font = attributes[.font] as? UIFont {
            return font.fontDescriptor.symbolicTraits.contains(.traitBold)
        }
        return false
    }

    /// 检查当前是否为斜体
    static func getCurrentItalicState(textView: UITextView) -> Bool {
        let attributes = getCurrentAttributes(textView: textView)
        if let font = attributes[.font] as? UIFont {
            return font.fontDescriptor.symbolicTraits.contains(.traitItalic)
        }
        return false
    }

    /// 检查当前是否有下划线
    static func getCurrentUnderlineState(textView: UITextView) -> Bool {
        let attributes = getCurrentAttributes(textView: textView)
        if let underlineStyle = attributes[.underlineStyle] as? Int {
            return underlineStyle != 0
        }
        return false
    }

    /// 获取当前文字颜色
    static func getCurrentTextColor(textView: UITextView) -> UIColor {
        let attributes = getCurrentAttributes(textView: textView)
        return attributes[.foregroundColor] as? UIColor ?? UIColor.label
    }
}
