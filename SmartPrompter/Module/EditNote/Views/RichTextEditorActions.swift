//
//  RichTextEditorActions.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/19.
//

import UIKit
import Foundation

// MARK: - 通知名称扩展
extension Notification.Name {
    static let richTextEditorDidChangeFormat = Notification.Name("richTextEditorDidChangeFormat")
}

// MARK: - 富文本编辑器操作类
class RichTextEditorActions {
    
    // MARK: - 格式化操作
    
    /// 切换粗体格式
    static func toggleBold(textView: UITextView) {
        let selectedRange = textView.selectedRange

        if selectedRange.length > 0 {
            // 有选中文本，切换选中文本的粗体格式
            toggleBoldForRange(textView: textView, range: selectedRange)
        } else {
            // 无选中文本，切换输入属性的粗体格式
            toggleBoldForTypingAttributes(textView: textView)
        }

        // 触发状态更新
        DispatchQueue.main.async {
            NotificationCenter.default.post(name: .richTextEditorDidChangeFormat, object: textView)
        }
    }
    
    /// 切换斜体格式
    static func toggleItalic(textView: UITextView) {
        let selectedRange = textView.selectedRange

        if selectedRange.length > 0 {
            // 有选中文本，切换选中文本的斜体格式
            toggleItalicForRange(textView: textView, range: selectedRange)
        } else {
            // 无选中文本，切换输入属性的斜体格式
            toggleItalicForTypingAttributes(textView: textView)
        }

        // 触发状态更新
        DispatchQueue.main.async {
            NotificationCenter.default.post(name: .richTextEditorDidChangeFormat, object: textView)
        }
    }
    
    /// 切换下划线格式
    static func toggleUnderline(textView: UITextView) {
        let selectedRange = textView.selectedRange

        if selectedRange.length > 0 {
            // 有选中文本，切换选中文本的下划线格式
            toggleUnderlineForRange(textView: textView, range: selectedRange)
        } else {
            // 无选中文本，切换输入属性的下划线格式
            toggleUnderlineForTypingAttributes(textView: textView)
        }

        // 触发状态更新
        DispatchQueue.main.async {
            NotificationCenter.default.post(name: .richTextEditorDidChangeFormat, object: textView)
        }
    }
    
    /// 应用文字颜色
    static func applyTextColor(textView: UITextView, color: UIColor) {
        let selectedRange = textView.selectedRange

        if selectedRange.length > 0 {
            // 有选中文本，应用颜色到选中文本
            applyTextColorForRange(textView: textView, range: selectedRange, color: color)
        } else {
            // 无选中文本，设置输入属性的颜色
            applyTextColorForTypingAttributes(textView: textView, color: color)
        }

        // 触发状态更新
        DispatchQueue.main.async {
            NotificationCenter.default.post(name: .richTextEditorDidChangeFormat, object: textView)
        }
    }
    
    // MARK: - 私有方法 - 粗体操作
    
    private static func toggleBoldForRange(textView: UITextView, range: NSRange) {
        let textStorage = textView.textStorage
        
        textStorage.beginEditing()
        
        textStorage.enumerateAttribute(.font, in: range, options: []) { (value, subRange, _) in
            let currentFont = value as? UIFont ?? UIFont.systemFont(ofSize: 17)
            let isBold = currentFont.fontDescriptor.symbolicTraits.contains(.traitBold)
            let isItalic = currentFont.fontDescriptor.symbolicTraits.contains(.traitItalic)

            let newFont: UIFont
            if isBold {
                // 移除粗体，保持斜体
                newFont = isItalic ? UIFont.italicSystemFont(ofSize: currentFont.pointSize) : UIFont.systemFont(ofSize: currentFont.pointSize)
            } else {
                // 添加粗体，保持斜体
                newFont = isItalic ? createBoldItalicFont(size: currentFont.pointSize) : UIFont.boldSystemFont(ofSize: currentFont.pointSize)
            }

            textStorage.addAttribute(.font, value: newFont, range: subRange)
        }
        
        textStorage.endEditing()
    }

    private static func toggleBoldForTypingAttributes(textView: UITextView) {
        var typingAttributes = textView.typingAttributes
        let currentFont = typingAttributes[.font] as? UIFont ?? UIFont.systemFont(ofSize: 17)
        let isBold = currentFont.fontDescriptor.symbolicTraits.contains(.traitBold)
        let isItalic = currentFont.fontDescriptor.symbolicTraits.contains(.traitItalic)

        let newFont: UIFont
        if isBold {
            // 移除粗体，保持斜体
            if isItalic {
                newFont = UIFont.italicSystemFont(ofSize: currentFont.pointSize)
            } else {
                newFont = UIFont.systemFont(ofSize: currentFont.pointSize)
            }
        } else {
            // 添加粗体，保持斜体
            if isItalic {
                newFont = createBoldItalicFont(size: currentFont.pointSize)
            } else {
                newFont = UIFont.boldSystemFont(ofSize: currentFont.pointSize)
            }
        }

        typingAttributes[.font] = newFont
        textView.typingAttributes = typingAttributes
    }
    
    // MARK: - 私有方法 - 斜体操作
    
    private static func toggleItalicForRange(textView: UITextView, range: NSRange) {
        let textStorage = textView.textStorage
        
        textStorage.beginEditing()
        
        textStorage.enumerateAttribute(.font, in: range, options: []) { (value, subRange, _) in
            let currentFont = value as? UIFont ?? UIFont.systemFont(ofSize: 17)
            let isBold = currentFont.fontDescriptor.symbolicTraits.contains(.traitBold)
            let isItalic = currentFont.fontDescriptor.symbolicTraits.contains(.traitItalic)

            let newFont: UIFont
            if isItalic {
                // 移除斜体，保持粗体
                newFont = isBold ? UIFont.boldSystemFont(ofSize: currentFont.pointSize) : UIFont.systemFont(ofSize: currentFont.pointSize)
            } else {
                // 添加斜体，保持粗体
                newFont = isBold ? createBoldItalicFont(size: currentFont.pointSize) : UIFont.italicSystemFont(ofSize: currentFont.pointSize)
            }

            textStorage.addAttribute(.font, value: newFont, range: subRange)
        }
        
        textStorage.endEditing()
    }

    private static func toggleItalicForTypingAttributes(textView: UITextView) {
        var typingAttributes = textView.typingAttributes
        let currentFont = typingAttributes[.font] as? UIFont ?? UIFont.systemFont(ofSize: 17)
        let isBold = currentFont.fontDescriptor.symbolicTraits.contains(.traitBold)
        let isItalic = currentFont.fontDescriptor.symbolicTraits.contains(.traitItalic)

        let newFont: UIFont
        if isItalic {
            // 移除斜体，保持粗体
            if isBold {
                newFont = UIFont.boldSystemFont(ofSize: currentFont.pointSize)
            } else {
                newFont = UIFont.systemFont(ofSize: currentFont.pointSize)
            }
        } else {
            // 添加斜体，保持粗体
            if isBold {
                newFont = createBoldItalicFont(size: currentFont.pointSize)
            } else {
                newFont = UIFont.italicSystemFont(ofSize: currentFont.pointSize)
            }
        }

        typingAttributes[.font] = newFont
        textView.typingAttributes = typingAttributes
    }
    
    // MARK: - 私有方法 - 下划线操作
    
    private static func toggleUnderlineForRange(textView: UITextView, range: NSRange) {
        let textStorage = textView.textStorage
        
        textStorage.beginEditing()
        
        let currentUnderlineStyle = textStorage.attribute(.underlineStyle, at: range.location, effectiveRange: nil) as? Int ?? 0
        let hasUnderline = currentUnderlineStyle != 0

        if hasUnderline {
            textStorage.removeAttribute(.underlineStyle, range: range)
        } else {
            textStorage.addAttribute(.underlineStyle, value: NSUnderlineStyle.single.rawValue, range: range)
        }
        
        textStorage.endEditing()
    }

    private static func toggleUnderlineForTypingAttributes(textView: UITextView) {
        var typingAttributes = textView.typingAttributes
        let currentUnderlineStyle = typingAttributes[.underlineStyle] as? Int ?? 0
        let hasUnderline = currentUnderlineStyle != 0

        if hasUnderline {
            // 移除下划线
            typingAttributes.removeValue(forKey: .underlineStyle)
        } else {
            // 添加下划线
            typingAttributes[.underlineStyle] = NSUnderlineStyle.single.rawValue
        }

        textView.typingAttributes = typingAttributes
    }
    
    // MARK: - 私有方法 - 颜色操作
    
    private static func applyTextColorForRange(textView: UITextView, range: NSRange, color: UIColor) {
        let textStorage = textView.textStorage
        
        textStorage.beginEditing()
        textStorage.addAttribute(.foregroundColor, value: color, range: range)
        textStorage.endEditing()
    }

    private static func applyTextColorForTypingAttributes(textView: UITextView, color: UIColor) {
        var typingAttributes = textView.typingAttributes
        typingAttributes[.foregroundColor] = color
        textView.typingAttributes = typingAttributes
    }
    
    // MARK: - 工具方法
    
    /// 获取当前选中文本的属性
    static func getCurrentAttributes(textView: UITextView) -> [NSAttributedString.Key: Any] {
        let selectedRange = textView.selectedRange
        
        if selectedRange.length > 0 {
            // 有选中文本，返回选中文本的属性
            return textView.attributedText.attributes(at: selectedRange.location, effectiveRange: nil)
        } else if selectedRange.location > 0 {
            // 光标位置，返回前一个字符的属性
            return textView.attributedText.attributes(at: selectedRange.location - 1, effectiveRange: nil)
        } else {
            // 开头位置，返回输入属性
            return textView.typingAttributes
        }
    }
    
    /// 检查当前是否为粗体
    static func isBold(textView: UITextView) -> Bool {
        let attributes = getCurrentAttributes(textView: textView)
        if let font = attributes[.font] as? UIFont {
            return font.fontDescriptor.symbolicTraits.contains(.traitBold)
        }
        return false
    }
    
    /// 检查当前是否为斜体
    static func isItalic(textView: UITextView) -> Bool {
        let attributes = getCurrentAttributes(textView: textView)
        if let font = attributes[.font] as? UIFont {
            return font.fontDescriptor.symbolicTraits.contains(.traitItalic)
        }
        return false
    }
    
    /// 检查当前是否有下划线
    static func isUnderlined(textView: UITextView) -> Bool {
        let attributes = getCurrentAttributes(textView: textView)
        if let underlineStyle = attributes[.underlineStyle] as? Int {
            return underlineStyle != 0
        }
        return false
    }
    
    /// 获取当前文字颜色
    static func getCurrentTextColor(textView: UITextView) -> UIColor {
        let attributes = getCurrentAttributes(textView: textView)
        return attributes[.foregroundColor] as? UIColor ?? UIColor.label
    }

    // MARK: - 基于状态的格式化操作

    /// 根据期望状态设置粗体格式
    static func setBold(textView: UITextView, shouldBeBold: Bool) {
        let selectedRange = textView.selectedRange

        if selectedRange.length > 0 {
            // 有选中文本，设置选中文本的粗体格式
            setBoldForRange(textView: textView, range: selectedRange, shouldBeBold: shouldBeBold)
        } else {
            // 无选中文本，设置输入属性的粗体格式
            setBoldForTypingAttributes(textView: textView, shouldBeBold: shouldBeBold)
        }
    }

    /// 根据期望状态设置斜体格式
    static func setItalic(textView: UITextView, shouldBeItalic: Bool) {
        let selectedRange = textView.selectedRange

        if selectedRange.length > 0 {
            // 有选中文本，设置选中文本的斜体格式
            setItalicForRange(textView: textView, range: selectedRange, shouldBeItalic: shouldBeItalic)
        } else {
            // 无选中文本，设置输入属性的斜体格式
            setItalicForTypingAttributes(textView: textView, shouldBeItalic: shouldBeItalic)
        }
    }

    /// 根据期望状态设置下划线格式
    static func setUnderline(textView: UITextView, shouldBeUnderlined: Bool) {
        let selectedRange = textView.selectedRange

        if selectedRange.length > 0 {
            // 有选中文本，设置选中文本的下划线格式
            setUnderlineForRange(textView: textView, range: selectedRange, shouldBeUnderlined: shouldBeUnderlined)
        } else {
            // 无选中文本，设置输入属性的下划线格式
            setUnderlineForTypingAttributes(textView: textView, shouldBeUnderlined: shouldBeUnderlined)
        }
    }

    // MARK: - 基于状态的私有方法

    /// 为选中范围设置粗体格式
    private static func setBoldForRange(textView: UITextView, range: NSRange, shouldBeBold: Bool) {
        // 直接使用 textStorage 修改属性，避免重设 attributedText
        let textStorage = textView.textStorage
        textStorage.beginEditing()
        
        textStorage.enumerateAttribute(.font, in: range, options: []) { (value, subRange, _) in
            let currentFont = value as? UIFont ?? UIFont.systemFont(ofSize: 17)
//            let isCurrentlyBold = currentFont.fontDescriptor.symbolicTraits.contains(.traitBold)
            let isCurrentlyItalic = currentFont.fontDescriptor.symbolicTraits.contains(.traitItalic)

            let newFont: UIFont
            if shouldBeBold && isCurrentlyItalic {
                newFont = createBoldItalicFont(size: currentFont.pointSize)
            } else if shouldBeBold && !isCurrentlyItalic {
                newFont = UIFont.boldSystemFont(ofSize: currentFont.pointSize)
            } else if !shouldBeBold && isCurrentlyItalic {
                newFont = UIFont.italicSystemFont(ofSize: currentFont.pointSize)
            } else {
                newFont = UIFont.systemFont(ofSize: currentFont.pointSize)
            }

            textStorage.addAttribute(.font, value: newFont, range: subRange)
        }
        
        textStorage.endEditing()
    }

    /// 为输入属性设置粗体格式
    private static func setBoldForTypingAttributes(textView: UITextView, shouldBeBold: Bool) {
        var typingAttributes = textView.typingAttributes
        let currentFont = typingAttributes[.font] as? UIFont ?? UIFont.systemFont(ofSize: 17)
        let isCurrentlyItalic = currentFont.fontDescriptor.symbolicTraits.contains(.traitItalic)

        let newFont: UIFont
        if shouldBeBold && isCurrentlyItalic {
            // 需要粗体且当前是斜体 -> 粗体斜体
            newFont = createBoldItalicFont(size: currentFont.pointSize)
        } else if shouldBeBold && !isCurrentlyItalic {
            // 需要粗体且当前不是斜体 -> 粗体
            newFont = UIFont.boldSystemFont(ofSize: currentFont.pointSize)
        } else if !shouldBeBold && isCurrentlyItalic {
            // 不需要粗体且当前是斜体 -> 斜体
            newFont = UIFont.italicSystemFont(ofSize: currentFont.pointSize)
        } else {
            // 不需要粗体且当前不是斜体 -> 普通
            newFont = UIFont.systemFont(ofSize: currentFont.pointSize)
        }

        typingAttributes[.font] = newFont
        textView.typingAttributes = typingAttributes
    }

    /// 为选中范围设置斜体格式
    private static func setItalicForRange(textView: UITextView, range: NSRange, shouldBeItalic: Bool) {
        let textStorage = textView.textStorage
        
        textStorage.beginEditing()
        
        textStorage.enumerateAttribute(.font, in: range, options: []) { (value, subRange, _) in
            let currentFont = value as? UIFont ?? UIFont.systemFont(ofSize: 17)
            let isCurrentlyBold = currentFont.fontDescriptor.symbolicTraits.contains(.traitBold)

            let newFont: UIFont
            if shouldBeItalic && isCurrentlyBold {
                newFont = createBoldItalicFont(size: currentFont.pointSize)
            } else if shouldBeItalic && !isCurrentlyBold {
                newFont = UIFont.italicSystemFont(ofSize: currentFont.pointSize)
            } else if !shouldBeItalic && isCurrentlyBold {
                newFont = UIFont.boldSystemFont(ofSize: currentFont.pointSize)
            } else {
                newFont = UIFont.systemFont(ofSize: currentFont.pointSize)
            }

            textStorage.addAttribute(.font, value: newFont, range: subRange)
        }
        
        textStorage.endEditing()
    }

    /// 为输入属性设置斜体格式
    private static func setItalicForTypingAttributes(textView: UITextView, shouldBeItalic: Bool) {
        var typingAttributes = textView.typingAttributes
        let currentFont = typingAttributes[.font] as? UIFont ?? UIFont.systemFont(ofSize: 17)
        let isCurrentlyBold = currentFont.fontDescriptor.symbolicTraits.contains(.traitBold)

        let newFont: UIFont
        if shouldBeItalic && isCurrentlyBold {
            // 需要斜体且当前是粗体 -> 粗体斜体
            newFont = createBoldItalicFont(size: currentFont.pointSize)
        } else if shouldBeItalic && !isCurrentlyBold {
            // 需要斜体且当前不是粗体 -> 斜体
            newFont = UIFont.italicSystemFont(ofSize: currentFont.pointSize)
        } else if !shouldBeItalic && isCurrentlyBold {
            // 不需要斜体且当前是粗体 -> 粗体
            newFont = UIFont.boldSystemFont(ofSize: currentFont.pointSize)
        } else {
            // 不需要斜体且当前不是粗体 -> 普通
            newFont = UIFont.systemFont(ofSize: currentFont.pointSize)
        }

        typingAttributes[.font] = newFont
        textView.typingAttributes = typingAttributes
    }

    /// 为选中范围设置下划线格式
    private static func setUnderlineForRange(textView: UITextView, range: NSRange, shouldBeUnderlined: Bool) {
        let textStorage = textView.textStorage
        
        textStorage.beginEditing()
        
        if shouldBeUnderlined {
            textStorage.addAttribute(.underlineStyle, value: NSUnderlineStyle.single.rawValue, range: range)
        } else {
            textStorage.removeAttribute(.underlineStyle, range: range)
        }
        
        textStorage.endEditing()
    }
    /// 为输入属性设置下划线格式
    private static func setUnderlineForTypingAttributes(textView: UITextView, shouldBeUnderlined: Bool) {
        var typingAttributes = textView.typingAttributes

        if shouldBeUnderlined {
            typingAttributes[.underlineStyle] = NSUnderlineStyle.single.rawValue
        } else {
            typingAttributes.removeValue(forKey: .underlineStyle)
        }

        textView.typingAttributes = typingAttributes
    }

    // MARK: - 辅助方法

    /// 创建粗体斜体字体
    private static func createBoldItalicFont(size: CGFloat) -> UIFont {
        let descriptor = UIFont.systemFont(ofSize: size).fontDescriptor
        let boldItalicTraits: UIFontDescriptor.SymbolicTraits = [.traitBold, .traitItalic]
        if let boldItalicDescriptor = descriptor.withSymbolicTraits(boldItalicTraits) {
            return UIFont(descriptor: boldItalicDescriptor, size: size)
        }
        // 如果无法创建粗体斜体，回退到粗体
        return UIFont.boldSystemFont(ofSize: size)
    }
}
