//
//  RichTextToolbar.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/19.
//

import SwiftUI
import UIKit

// MARK: - 富文本工具栏
struct RichTextToolbar: View {
    @ObservedObject var editorState: RichTextEditorState
    @Binding var attributedText: NSAttributedString
    @State private var showColorPicker = false
    @State private var selectedColor: Color = .primary
    
    var body: some View {
        ZStack {
            // 主工具栏
            HStack(spacing: AppConstrants.Spacing.m) {
                // 左侧格式化按钮组
                HStack(spacing: AppConstrants.Spacing.s) {
                    formatButton(
                        icon: "bold",
                        isActive: editorState.isBold,
                        action: toggleBold
                    )

                    formatButton(
                        icon: "italic",
                        isActive: editorState.isItalic,
                        action: toggleItalic
                    )

                    formatButton(
                        icon: "underline",
                        isActive: editorState.isUnderlined,
                        action: toggleUnderline
                    )

//                    colorPickerButton()
                }

                Spacer()

                // 右侧操作按钮组
                HStack(spacing: AppConstrants.Spacing.s) {
                    actionButton(
                        icon: "arrow.uturn.backward",
                        isEnabled: editorState.canUndo,
                        action: undo
                    )

                    actionButton(
                        icon: "arrow.uturn.forward",
                        isEnabled: editorState.canRedo,
                        action: redo
                    )

                    actionButton(
                        icon: "keyboard.chevron.compact.down",
                        isEnabled: true,
                        action: dismissKeyboard
                    )
                }
            }
            .padding(.horizontal, AppConstrants.Spacing.l)
            .padding(.vertical, AppConstrants.Spacing.m)
            .background(
                LinearGradient(
                    colors: [.appBackgroundSecondary, .appBackgroundSecondary.opacity(0.95)],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .clipShape(RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.l))
            .shadow(color: .black.opacity(0.03), radius: 20, x: 0, y: 0)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.l)
                    .stroke(.appLabelSecondary.opacity(0.1), lineWidth: 0.5)
            )

            // 颜色选择器 overlay
            if showColorPicker {
                colorPickerView()
                    .transition(.asymmetric(
                        insertion: .scale(scale: 0.8)
                            .combined(with: .opacity)
                            .combined(with: .move(edge: .bottom)),
                        removal: .scale(scale: 0.9)
                            .combined(with: .opacity)
                            .combined(with: .move(edge: .bottom))
                    ))
                    .zIndex(1) // 确保在最上层
            }
        }
        .onAppear {
            selectedColor = Color(editorState.currentTextColor)
        }
        .onChange(of: editorState.currentTextColor) { _, newColor in
            selectedColor = Color(newColor)
        }
        .animation(AppConstrants.Animation.spring, value: showColorPicker)
    }
    
    // MARK: - 格式化按钮
    @ViewBuilder
    private func formatButton(icon: String, isActive: Bool, action: @escaping () -> Void) -> some View {

        let isFinalActive = isActive && (self.editorState.textView?.selectedRange.length ?? 0 == 0)

        Button(action: {
            action()
        }) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundStyle(isFinalActive ? .white : .primary)
                .frame(width: 32, height: 32)
                .background(
                    Group {
                        if isFinalActive {
                            LinearGradient(
                                colors: [.accent, .accent.opacity(0.8)],
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        } else {
                            Color.clear
                        }
                    }
                )
                .clipShape(RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.m))
                .overlay(
                    RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.m)
                        .stroke(
                            isFinalActive ? .clear : .primary.opacity(0.12),
                            lineWidth: 1
                        )
                )
                .shadow(
                    color: isFinalActive ? .accent.opacity(0.3) : .clear,
                    radius: isFinalActive ? 4 : 0,
                    x: 0,
                    y: isFinalActive ? 2 : 0
                )
                .contentShape(Rectangle())
        }
        .toolbarButtonStyle()
    }
    
    // MARK: - 颜色选择按钮
    @ViewBuilder
    private func colorPickerButton() -> some View {
        Button(action: {
            showColorPicker.toggle()
        }) {
            ZStack {
                Image(systemName: "textformat")
                    .font(.appSubheadline)
                    .fontWeight(.medium)
                    .foregroundStyle(.primary)

                // 颜色指示器
                Rectangle()
                    .fill(selectedColor)
                    .frame(width: 20, height: 3)
                    .offset(y: 8)
            }
            .frame(width: 32, height: 32)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.s)
                    .stroke(Color.primary.opacity(0.2), lineWidth: AppConstrants.Border.thin)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - 操作按钮
    @ViewBuilder
    private func actionButton(icon: String, isEnabled: Bool, action: @escaping () -> Void) -> some View {
        Button(action: {
            action()
        }) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundStyle(isEnabled ? .primary : .secondary)
                .frame(width: 32, height: 32)
                .background(.regularMaterial)
                .clipShape(RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.m))
                .overlay(
                    RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.m)
                        .stroke(.primary.opacity(0.08), lineWidth: 1)
                )
                .shadow(AppConstrants.Shadow.light)
                .opacity(isEnabled ? 1.0 : 0.5)
        }
        .toolbarButtonStyle()
        .disabled(!isEnabled)
    }
    
    // MARK: - 颜色选择器视图
    @ViewBuilder
    private func colorPickerView() -> some View {
        // 简洁的颜色选择器，6x3布局
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: AppConstrants.Spacing.xs), count: 6), spacing: AppConstrants.Spacing.xs) {
            ForEach(presetColors, id: \.self) { color in
                colorSwatch(color: color)
            }
        }
        .padding(AppConstrants.Spacing.m)
        .background(.regularMaterial)
        .clipShape(RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.l))
        .shadow(AppConstrants.Shadow.light)
        .background(
            // 透明背景，点击关闭
            Color.clear
                .contentShape(Rectangle())
                .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
                .onTapGesture {
                    showColorPicker = false
                }
        )
    }
    
    // MARK: - 颜色样本
    @ViewBuilder
    private func colorSwatch(color: Color) -> some View {
        Button(action: {
            selectedColor = color
            applyTextColor(UIColor(color))
        }) {
            Circle()
                .fill(color)
                .frame(width: 28, height: 28)
                .overlay(
                    Circle()
                        .stroke(Color.primary.opacity(0.2), lineWidth: AppConstrants.Border.thin)
                )
                .overlay(
                    Circle()
                        .stroke(Color.accent, lineWidth: 2)
                        .opacity(selectedColor == color ? 1 : 0)
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - 预设颜色 (6x3布局)
    private var presetColors: [Color] {
        [
            // 第一行：基础颜色
            .black, .gray, .white, .red, .orange, .yellow,
            // 第二行：彩色
            .green, .blue, .purple, .pink, .brown, .cyan,
            // 第三行：深色调
            Color(.systemRed), Color(.systemOrange), Color(.systemYellow),
            Color(.systemGreen), Color(.systemBlue), Color(.systemPurple)
        ]
    }
}

// MARK: - 工具栏操作
extension RichTextToolbar {
    
    private func toggleBold() {
        guard let textView = editorState.textView else { return }

        if textView.selectedRange.length > 0 {
            // 执行格式化操作
            RichTextEditorActions.toggleBold(textView: textView)
            // 关键：同步更新 SwiftUI 的 @Binding 数据
            // 这样 updateUIView 就不会用旧数据覆盖新内容
            attributedText = textView.attributedText

        } else {
            // 根据当前按钮状态决定应该应用什么格式
            let shouldBeBold = !editorState.isBold

            // 执行格式化操作
            RichTextEditorActions.setBold(textView: textView, shouldBeBold: shouldBeBold)

            // 关键：同步更新 SwiftUI 的 @Binding 数据
            // 这样 updateUIView 就不会用旧数据覆盖新内容
            attributedText = textView.attributedText

            // 更新按钮状态
            editorState.updateFormatState(bold: shouldBeBold)
        }
        
        // 更新撤销重做状态
        editorState.updateUndoRedoStates()
    }

    private func toggleItalic() {
        guard let textView = editorState.textView else { return }
        
        if textView.selectedRange.length > 0 {
            RichTextEditorActions.toggleItalic(textView: textView)
            attributedText = textView.attributedText
            
        } else {
            // 根据当前按钮状态决定应该应用什么格式
            let shouldBeItalic = !editorState.isItalic

            // 执行格式化操作
            RichTextEditorActions.setItalic(textView: textView, shouldBeItalic: shouldBeItalic)

            // 同步更新 SwiftUI 的 @Binding 数据
            attributedText = textView.attributedText

            // 更新按钮状态
            editorState.updateFormatState(italic: shouldBeItalic)
        }
        
        // 更新撤销重做状态
        editorState.updateUndoRedoStates()
    }

    private func toggleUnderline() {
        guard let textView = editorState.textView else { return }

        if textView.selectedRange.length > 0 {
            RichTextEditorActions.toggleUnderline(textView: textView)
            attributedText = textView.attributedText

        } else {
            // 根据当前按钮状态决定应该应用什么格式
            let shouldBeUnderlined = !editorState.isUnderlined

            // 执行格式化操作
            RichTextEditorActions.setUnderline(textView: textView, shouldBeUnderlined: shouldBeUnderlined)

            // 同步更新 SwiftUI 的 @Binding 数据
            attributedText = textView.attributedText

            // 更新按钮状态
            editorState.updateFormatState(underlined: shouldBeUnderlined)
        }

        // 更新撤销重做状态
        editorState.updateUndoRedoStates()
    }

    private func applyTextColor(_ color: UIColor) {
        guard let textView = editorState.textView else { return }

        // 更新按钮状态（显示选中的颜色）
        editorState.updateFormatState(color: color)

        // 执行颜色应用操作
        RichTextEditorActions.applyTextColor(textView: textView, color: color)

        // 同步更新 SwiftUI 的 @Binding 数据
        attributedText = textView.attributedText

        // 更新撤销重做状态
        editorState.updateUndoRedoStates()

        // 关闭颜色选择器
        showColorPicker = false
    }

    private func undo() {
        guard let textView = editorState.textView else { return }
        textView.undoManager?.undo()

        // 同步更新 SwiftUI 的 @Binding 数据
        attributedText = textView.attributedText

        // 更新撤销重做状态
        editorState.updateUndoRedoStates()
        
        // 重置状态
        editorState.resetFormatStates()
    }

    private func redo() {
        guard let textView = editorState.textView else { return }
        textView.undoManager?.redo()

        // 同步更新 SwiftUI 的 @Binding 数据
        attributedText = textView.attributedText

        // 更新撤销重做状态
        editorState.updateUndoRedoStates()
        
        // 重置状态
        editorState.resetFormatStates()
    }
    
    private func dismissKeyboard() {
        guard let textView = editorState.textView else { return }
        textView.resignFirstResponder()
    }
}
