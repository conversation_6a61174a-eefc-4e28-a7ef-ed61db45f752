# SwiftUI 状态更新问题解决方案

## 问题描述

在富文本编辑器中遇到了 SwiftUI 错误：
```
Publishing changes from within view updates is not allowed, this will cause undefined behavior.
```

## 问题根本原因

在 UITextView 的 delegate 方法（如 `textViewDidChangeSelection`）中直接修改 `@Published` 属性，导致在 SwiftUI 视图更新周期内触发新的状态更新。

## 解决方案核心思路

根据用户需求分析，状态更新应该遵循以下逻辑：

1. **选中文本时**：重置所有格式按钮状态为未选中
2. **点击格式化按钮时**：更新对应按钮的状态
3. **避免过度更新**：不在每次文本变化、光标移动时都更新格式状态

## 具体实现

### 1. 状态管理类简化

```swift
class RichTextEditorState: ObservableObject {
    @Published var canUndo: Bool = false
    @Published var canRedo: Bool = false
    @Published var isBold: Bool = false
    @Published var isItalic: Bool = false
    @Published var isUnderlined: Bool = false
    @Published var currentTextColor: UIColor = UIColor.label

    weak var textView: UITextView?
    
    // 重置所有格式状态（选中文本时调用）
    func resetFormatStates() {
        isBold = false
        isItalic = false
        isUnderlined = false
        currentTextColor = UIColor.label
    }
    
    // 更新格式状态（点击格式化按钮后调用）
    func updateFormatState(bold: Bool? = nil, italic: Bool? = nil, underlined: Bool? = nil, color: UIColor? = nil) {
        if let bold = bold { isBold = bold }
        if let italic = italic { isItalic = italic }
        if let underlined = underlined { isUnderlined = underlined }
        if let color = color { currentTextColor = color }
    }
    
    // 更新撤销重做状态
    func updateUndoRedoStates() {
        guard let textView = textView else { return }
        canUndo = textView.undoManager?.canUndo ?? false
        canRedo = textView.undoManager?.canRedo ?? false
    }
}
```

### 2. UITextView Delegate 方法优化

```swift
func textViewDidChange(_ textView: UITextView) {
    parent.attributedText = textView.attributedText
    // 只更新撤销重做状态
    DispatchQueue.main.async {
        self.parent.editorState.updateUndoRedoStates()
    }
}

func textViewDidChangeSelection(_ textView: UITextView) {
    let range = textView.selectedRange
    
    // 如果选中了文本，重置所有格式状态
    if range.length > 0 {
        DispatchQueue.main.async {
            self.parent.editorState.resetFormatStates()
        }
    }
}
```

### 3. 工具栏按钮操作

```swift
private func toggleBold() {
    guard let textView = editorState.textView else { return }
    
    // 执行格式化操作
    RichTextEditorActions.toggleBold(textView: textView)
    
    // 更新按钮状态
    let newBoldState = RichTextEditor.getCurrentBoldState(textView: textView)
    editorState.updateFormatState(bold: newBoldState)
    
    // 更新撤销重做状态
    editorState.updateUndoRedoStates()
}
```

## 关键改进点

1. **避免在 delegate 方法中直接更新 @Published 属性**
2. **使用 DispatchQueue.main.async 确保状态更新在正确的时机**
3. **简化状态更新逻辑，只在必要时更新**
4. **分离关注点：选中文本重置状态，点击按钮更新状态**

## 测试结果

- ✅ 构建成功，无编译错误
- ✅ 解决了 SwiftUI 状态更新错误
- ✅ 保持了富文本编辑功能的完整性
- ✅ 按钮状态正确响应用户操作

## 总结

通过重新设计状态管理逻辑，避免在 SwiftUI 视图更新周期内触发新的状态更新，成功解决了 "Publishing changes from within view updates is not allowed" 错误。解决方案简洁、高效，符合 SwiftUI 的最佳实践。
