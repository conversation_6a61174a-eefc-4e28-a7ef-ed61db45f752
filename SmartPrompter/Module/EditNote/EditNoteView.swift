//
//  EditNoteView.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/19.
//

import SwiftUI

// MARK: - 编辑台本
struct EditNoteView: View {
    
    @State var viewModel: NoteViewModel

    /// 编辑的台本
    var note: Note
    /// 是否是从编辑过来的
    var isFromEdit: Bool = false

    @Environment(\.dismiss) var dismiss

    /// 当前标题编辑状态
    @FocusState var titleEditFocus: Bool
    /// 当前正文编辑状态
    @FocusState var contentEditFocus: Bool
    /// 当前标题的文本
    @State var title: String = ""
    /// 当前正文的文本
    @State var content: NSAttributedString = NSAttributedString(string: "")
    /// 富文本编辑器状态管理
    @StateObject var richTextEditorState = RichTextEditorState()
    
    init(viewModel: NoteViewModel, note: Note?) {
        if let note = note {
            self.note = note
            self.isFromEdit = true
            self.title = note.title
            self.content = note.getRichText() ?? NSAttributedString(string: "")
        } else {
            self.note = Note.createRichTextNote(title: "",
                                                richText: NSAttributedString(""),
                                                folder: nil)
        }
        self.viewModel = viewModel
    }
    
    var body: some View {
        VStack(spacing: AppConstrants.Spacing.l) {
            // 顶部导航栏
            topBarSection()
            // 输入视图
            editView()
        }
        .background(.appBackgroundPrimary)
        .ignoresSafeArea(edges: .top)
    }
}
