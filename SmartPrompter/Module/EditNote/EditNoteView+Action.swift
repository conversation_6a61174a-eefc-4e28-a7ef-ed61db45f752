//
//  EditNoteView+Action.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/19.
//

import Foundation
import SwiftUI

// MARK: - 事件
extension EditNoteView {

    /// 编辑完成
    func actionForEditDone() {
        // 如果正文为空 直接返回
        if content.string.isEmpty {
            dismiss()
            return
        }
        // 保存富文本内容
        note.title = title.isEmpty ? "no name prompt" : title
        note.setRichText(content)

        if isFromEdit {
            viewModel.updateNoteRichText(note)
        } else {
            viewModel.addNote(note: note)
        }
        // 关闭编辑界面
        dismiss()
    }

    /// 编辑取消
    func actionForEditCancel() {
        // 检查是否有未保存的更改
        let hasChanges = title != note.title || content != (note.getRichText() ?? NSAttributedString(string: ""))

        if hasChanges {
            // 如果有更改，可以在这里添加确认对话框
            // 目前直接关闭

        }

        dismiss()
    }
}
