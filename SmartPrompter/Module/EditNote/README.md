# 富文本编辑器模块

## 概述

本模块为 SmartPrompter 应用提供了完整的富文本编辑功能，支持文本格式化、颜色选择、撤销重做等操作，严格遵循 Apple Human Interface Guidelines。

## 功能特性

### 🎨 文本格式化
- **粗体**：支持选中文本或光标位置的粗体格式切换
- **斜体**：支持选中文本或光标位置的斜体格式切换
- **下划线**：支持选中文本或光标位置的下划线格式切换

### 🌈 颜色选择
- **预设颜色**：提供12种常用颜色快速选择
- **自定义颜色**：支持完整的颜色选择器
- **实时预览**：工具栏显示当前文字颜色

### ⚡ 操作功能
- **撤销/重做**：完整的撤销重做支持
- **键盘控制**：一键收起键盘
- **状态同步**：实时显示格式状态

### 📱 用户体验
- **触觉反馈**：所有操作都有相应的触觉反馈
- **动画效果**：工具栏显示/隐藏的流畅动画
- **深色模式**：完整支持深色和浅色模式
- **可访问性**：遵循 iOS 可访问性标准

## 核心组件

### 1. RichTextEditor
主要的富文本编辑器组件，基于 UITextView 实现。

```swift
struct RichTextEditor: UIViewRepresentable {
    @Binding var attributedText: NSAttributedString
    @ObservedObject var editorState: RichTextEditorState
}
```

**特性：**
- 支持富文本编辑
- 实时状态同步
- 选择范围跟踪
- 撤销重做管理

### 2. RichTextEditorState
富文本编辑器的状态管理类。

```swift
class RichTextEditorState: ObservableObject {
    @Published var selectedRange: NSRange
    @Published var currentAttributes: [NSAttributedString.Key: Any]
    @Published var canUndo: Bool
    @Published var canRedo: Bool
    @Published var isBold: Bool
    @Published var isItalic: Bool
    @Published var isUnderlined: Bool
    @Published var currentTextColor: UIColor
}
```

**功能：**
- 跟踪当前选择范围
- 监控格式状态变化
- 管理撤销重做状态
- 提供格式查询接口

### 3. RichTextToolbar
富文本编辑工具栏组件。

```swift
struct RichTextToolbar: View {
    @ObservedObject var editorState: RichTextEditorState
}
```

**布局：**
- **左侧**：格式化按钮（粗体、斜体、下划线、颜色）
- **右侧**：操作按钮（撤销、重做、键盘收起）

### 4. RichTextEditorActions
富文本编辑操作的实现类。

```swift
class RichTextEditorActions {
    static func toggleBold(textView: UITextView)
    static func toggleItalic(textView: UITextView)
    static func toggleUnderline(textView: UITextView)
    static func applyTextColor(textView: UITextView, color: UIColor)
}
```

**功能：**
- 处理格式化操作
- 管理选中文本和光标位置的格式
- 提供格式状态查询

## 使用方法

### 基础使用

```swift
struct ContentView: View {
    @State private var attributedText = NSAttributedString(string: "")
    @StateObject private var editorState = RichTextEditorState()
    @FocusState private var isEditorFocused: Bool
    
    var body: some View {
        VStack {
            // 富文本编辑器
            RichTextEditor(attributedText: $attributedText, editorState: editorState)
                .focused($isEditorFocused)
            
            // 工具栏（仅在聚焦时显示）
            if isEditorFocused {
                RichTextToolbar(editorState: editorState)
            }
        }
    }
}
```

### 在 EditNoteView 中的集成

```swift
@ViewBuilder
func editContentView() -> some View {
    VStack(spacing: 0) {
        // 富文本编辑器
        ZStack(alignment: .topLeading) {
            RichTextEditor(attributedText: $content, editorState: richTextEditorState)
                .focused($contentEditFocus)
            
            if content.string.isEmpty {
                Text("Enter Prompt Text")
                    .foregroundStyle(.appLabelSecondary.opacity(0.8))
            }
        }
        
        // 富文本工具栏
        if contentEditFocus {
            RichTextToolbar(editorState: richTextEditorState)
                .transition(.move(edge: .bottom).combined(with: .opacity))
        }
    }
    .animation(.easeInOut(duration: 0.3), value: contentEditFocus)
}
```

## 设计规范

### 视觉设计
- **按钮尺寸**：32x32pt，符合最小触摸目标要求
- **间距系统**：使用 AppConstrants.Spacing 统一间距
- **圆角设计**：使用 AppConstrants.CornerRadius 统一圆角
- **颜色系统**：支持深色和浅色模式自动切换

### 交互设计
- **即时反馈**：所有操作都有触觉反馈
- **状态指示**：按钮状态清晰可见
- **流畅动画**：工具栏显示隐藏动画
- **键盘适配**：工具栏跟随键盘显示

### 可访问性
- **VoiceOver 支持**：所有按钮都有适当的标签
- **动态字体**：支持系统字体大小调整
- **高对比度**：在高对比度模式下正常工作
- **减少动画**：尊重用户的动画偏好设置

## 技术实现

### 架构设计
- **MVVM 模式**：清晰的数据绑定和状态管理
- **组合式设计**：模块化的组件设计
- **状态驱动**：基于状态的 UI 更新
- **响应式编程**：使用 @Published 和 @ObservedObject

### 性能优化
- **延迟加载**：工具栏仅在需要时显示
- **状态缓存**：避免重复的属性查询
- **内存管理**：正确的对象生命周期管理
- **渲染优化**：最小化不必要的 UI 更新

### 兼容性
- **iOS 版本**：支持 iOS 15.0+
- **设备适配**：支持 iPhone 和 iPad
- **屏幕尺寸**：适配各种屏幕尺寸
- **方向支持**：支持横屏和竖屏

## 演示和测试

### 演示视图
提供了 `RichTextEditorDemo` 演示视图，展示所有功能：

```swift
struct RichTextEditorDemo: View {
    // 完整的演示实现
}
```

### 测试建议
1. **功能测试**：测试所有格式化功能
2. **状态测试**：验证状态同步正确性
3. **性能测试**：测试大文本处理性能
4. **可访问性测试**：验证 VoiceOver 支持
5. **设备测试**：在不同设备上测试

## 扩展和定制

### 添加新格式
要添加新的文本格式（如删除线），需要：

1. 在 `RichTextEditorState` 中添加状态属性
2. 在 `RichTextEditorActions` 中实现操作方法
3. 在 `RichTextToolbar` 中添加按钮
4. 更新状态检查逻辑

### 自定义工具栏
可以通过修改 `RichTextToolbar` 来自定义工具栏布局：

```swift
// 自定义按钮布局
HStack {
    // 左侧按钮组
    formatButtonGroup()
    
    Spacer()
    
    // 右侧按钮组
    actionButtonGroup()
}
```

### 主题定制
通过修改颜色和样式常量来定制主题：

```swift
// 自定义主题色
.foregroundStyle(isActive ? .customAccent : .primary)
.background(isActive ? .customAccent : Color.clear)
```

## 最佳实践

1. **状态管理**：始终通过 `RichTextEditorState` 管理状态
2. **性能考虑**：避免在大文本上频繁操作
3. **用户体验**：提供清晰的视觉反馈
4. **错误处理**：优雅处理边界情况
5. **测试覆盖**：确保所有功能都有测试覆盖

## 故障排除

### 常见问题
1. **格式不生效**：检查选择范围是否正确
2. **状态不同步**：确保正确调用状态更新方法
3. **性能问题**：检查是否有不必要的重复操作
4. **布局问题**：验证约束和间距设置

### 调试技巧
- 使用 `print` 语句跟踪状态变化
- 检查 `selectedRange` 的值
- 验证 `attributedText` 的属性
- 使用 Xcode 的视图调试器检查布局
