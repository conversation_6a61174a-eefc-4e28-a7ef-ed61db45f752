//
//  EditNoteView+TopBar.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/19.
//

import SwiftUI

// MARK: - 头部视图
extension EditNoteView {
    
    @ViewBuilder
    func topBarSection() -> some View {
        VStack {
            Spacer()
            ZStack {
                HStack(spacing: 0) {
                    Spacer()
                    if contentEditFocus {
                        textCountView()
                    } else {
                        Text(isFromEdit ? "Edit Script" : "New Script")
                            .font(.system(size: 17, weight: .semibold, design: .rounded))
                            .foregroundStyle(.appLabelPrimary)
                    }
                    Spacer()
                }
                .frame(maxWidth: .infinity)
                .padding(.horizontal, AppConstrants.Spacing.l)
                
                HStack(spacing: 0) {
                    cancelButton()
                    Spacer()
                    doneButton()
                }
                .padding(.horizontal, AppConstrants.Spacing.l)
            }
            .frame(height: AppConstrants.Size.Button.heightM)
            .padding(.bottom, AppConstrants.Spacing.xxs)
        }
        .frame(height: AppConstrants.safeAreaTop + AppConstrants.Size.Button.heightM)
        .background(Material.bar)
        .clipShape(UnevenRoundedRectangle(bottomLeadingRadius: AppConstrants.CornerRadius.xl, bottomTrailingRadius: AppConstrants.CornerRadius.xl))
        .shadow(color: .black.opacity(0.01), radius: 10, x: 0, y: 20)
    }
    
    @ViewBuilder
    func cancelButton() -> some View {
        Button {
            actionForEditCancel()
        } label: {
            Text("Back")
                .foregroundStyle(.appLabelPrimary)
                .font(.appFootnote).fontWeight(.semibold)
                .frame(width: 60, height: AppConstrants.Size.Button.heightXS)
                .appCornerRadiusAndBoard(radius: AppConstrants.CornerRadius.m,
                                         boardColor: .accent,
                                         lineWidth: 1.2)
        }
        .primaryButtonStyle()
    }
    
    @ViewBuilder
    func doneButton() -> some View {
        Button {
            actionForEditDone()
        } label: {
            Text("Save")
                .foregroundStyle(.appLabelOnDark)
                .font(.appFootnote).fontWeight(.semibold)
                .frame(width: 60, height: AppConstrants.Size.Button.heightXS)
                .background(.accent)
                .appCornerRadiusAndBoard(radius: AppConstrants.CornerRadius.m,
                                         boardColor: .accent,
                                         lineWidth: 1.2)
        }
        .primaryButtonStyle()
    }
    
    @ViewBuilder
    func textCountView() -> some View {
        VStack(spacing: AppConstrants.Spacing.xxs) {
            Text("\(content.string.count) characters")
                .font(.system(size: 12, weight: .semibold, design: .monospaced))
                .foregroundStyle(.accent)

            Text("≈ \(estimatedReadingTime)")
                .font(.system(size: 11, weight: .medium))
                .foregroundStyle(.appLabelSecondary.opacity(0.8))
        }
        .padding(.horizontal, AppConstrants.Spacing.s)
        .padding(.vertical, AppConstrants.Spacing.xs)
        .background(.accent.opacity(0.02))
        .clipShape(RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.s))
        .overlay(
            RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.s)
                .stroke(.accent.opacity(0.1), lineWidth: AppConstrants.AppBorder.ultraThin)
        )
    }

    /// 计算预计阅读时间
    private var estimatedReadingTime: String {
        let characterCount = content.string.count
        // 假设每分钟阅读200个字符（中英文混合）
        let totalSeconds = max(1, characterCount * 60 / 200)
        let minutes = totalSeconds / 60
        let seconds = totalSeconds % 60

        if minutes > 0 {
            return "\(minutes)m \(seconds)s"
        } else {
            return "\(seconds)s"
        }
    }
    
}
