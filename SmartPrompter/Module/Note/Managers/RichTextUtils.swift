//
//  RichTextUtils.swift
//  SmartTeleprompter
//
//  Created by ryan on 2025/7/18.
//

import Foundation
import UIKit

// MARK: - 富文本处理工具类

/// 富文本处理工具类
/// 提供NSAttributedString的序列化、反序列化和常用操作
final class RichTextUtils {
    
    // MARK: - 序列化相关
    
    /// 将NSAttributedString转换为Data
    /// - Parameter attributedString: 要序列化的富文本
    /// - Returns: 序列化后的Data，失败返回nil
    static func serialize(_ attributedString: NSAttributedString) -> Data? {
        do {
            // 使用NSKeyedArchiver进行序列化，这是iOS推荐的方式
            let data = try NSKeyedArchiver.archivedData(
                withRootObject: attributedString,
                requiringSecureCoding: false
            )
            return data
        } catch {
            print("富文本序列化失败: \(error)")
            return nil
        }
    }
    
    /// 将Data转换为NSAttributedString
    /// - Parameter data: 要反序列化的Data
    /// - Returns: 反序列化后的富文本，失败返回nil
    static func deserialize(_ data: Data) -> NSAttributedString? {
        do {
            // 使用NSKeyedUnarchiver进行反序列化（iOS 12+推荐方式）
            if #available(iOS 12.0, *) {
                guard let attributedString = try NSKeyedUnarchiver.unarchivedObject(ofClass: NSAttributedString.self, from: data) else {
                    print("富文本反序列化失败: 数据格式不正确")
                    return nil
                }
                return attributedString
            } else {
                // iOS 12以下的兼容方式
                guard let attributedString = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(data) as? NSAttributedString else {
                    print("富文本反序列化失败: 数据格式不正确")
                    return nil
                }
                return attributedString
            }
        } catch {
            print("富文本反序列化失败: \(error)")
            return nil
        }
    }
    
    /// 从纯文本创建NSAttributedString
    /// - Parameters:
    ///   - text: 纯文本内容
    ///   - font: 字体，默认为系统字体17号
    ///   - textColor: 文字颜色，默认为黑色
    /// - Returns: 创建的富文本
    static func createAttributedString(
        from text: String,
        font: UIFont = UIFont.systemFont(ofSize: 17),
        textColor: UIColor
    ) -> NSAttributedString {
        let attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: textColor
        ]
        return NSAttributedString(string: text, attributes: attributes)
    }
    
    // MARK: - 文本属性操作
    
    /// 为文本范围添加加粗样式
    /// - Parameters:
    ///   - attributedString: 可变富文本
    ///   - range: 要加粗的范围
    ///   - fontSize: 字体大小，默认为17
    static func addBoldStyle(
        to attributedString: NSMutableAttributedString,
        range: NSRange,
        fontSize: CGFloat = 17
    ) {
        let boldFont = UIFont.boldSystemFont(ofSize: fontSize)
        attributedString.addAttribute(.font, value: boldFont, range: range)
    }
    
    /// 为文本范围添加斜体样式
    /// - Parameters:
    ///   - attributedString: 可变富文本
    ///   - range: 要设置斜体的范围
    ///   - fontSize: 字体大小，默认为17
    static func addItalicStyle(
        to attributedString: NSMutableAttributedString,
        range: NSRange,
        fontSize: CGFloat = 17
    ) {
        let italicFont = UIFont.italicSystemFont(ofSize: fontSize)
        attributedString.addAttribute(.font, value: italicFont, range: range)
    }
    
    /// 为文本范围添加下划线
    /// - Parameters:
    ///   - attributedString: 可变富文本
    ///   - range: 要添加下划线的范围
    ///   - style: 下划线样式，默认为单线
    static func addUnderlineStyle(
        to attributedString: NSMutableAttributedString,
        range: NSRange,
        style: NSUnderlineStyle = .single
    ) {
        attributedString.addAttribute(.underlineStyle, value: style.rawValue, range: range)
    }
    
    /// 为文本范围添加删除线
    /// - Parameters:
    ///   - attributedString: 可变富文本
    ///   - range: 要添加删除线的范围
    ///   - style: 删除线样式，默认为单线
    static func addStrikethroughStyle(
        to attributedString: NSMutableAttributedString,
        range: NSRange,
        style: NSUnderlineStyle = .single
    ) {
        attributedString.addAttribute(.strikethroughStyle, value: style.rawValue, range: range)
    }
    
    /// 为文本范围设置颜色
    /// - Parameters:
    ///   - attributedString: 可变富文本
    ///   - range: 要设置颜色的范围
    ///   - color: 文字颜色
    static func setTextColor(
        to attributedString: NSMutableAttributedString,
        range: NSRange,
        color: UIColor
    ) {
        attributedString.addAttribute(.foregroundColor, value: color, range: range)
    }
    
    /// 为文本范围设置背景颜色
    /// - Parameters:
    ///   - attributedString: 可变富文本
    ///   - range: 要设置背景颜色的范围
    ///   - color: 背景颜色
    static func setBackgroundColor(
        to attributedString: NSMutableAttributedString,
        range: NSRange,
        color: UIColor
    ) {
        attributedString.addAttribute(.backgroundColor, value: color, range: range)
    }
    
    /// 为文本范围设置字体大小
    /// - Parameters:
    ///   - attributedString: 可变富文本
    ///   - range: 要设置字体大小的范围
    ///   - fontSize: 字体大小
    static func setFontSize(
        to attributedString: NSMutableAttributedString,
        range: NSRange,
        fontSize: CGFloat
    ) {
        let font = UIFont.systemFont(ofSize: fontSize)
        attributedString.addAttribute(.font, value: font, range: range)
    }
    
    // MARK: - 文本分析
    
    /// 获取富文本的纯文本内容
    /// - Parameter attributedString: 富文本
    /// - Returns: 纯文本内容
    static func getPlainText(from attributedString: NSAttributedString) -> String {
        return attributedString.string
    }
    
    /// 获取富文本的字符数
    /// - Parameter attributedString: 富文本
    /// - Returns: 字符数
    static func getCharacterCount(from attributedString: NSAttributedString) -> Int {
        return attributedString.length
    }
    
    /// 获取富文本的字数（中文按字计算，英文按单词计算）
    /// - Parameter attributedString: 富文本
    /// - Returns: 字数
    static func getWordCount(from attributedString: NSAttributedString) -> Int {
        let text = attributedString.string
        return getWordCount(from: text)
    }
    
    /// 获取纯文本的字数
    /// - Parameter text: 纯文本
    /// - Returns: 字数
    static func getWordCount(from text: String) -> Int {
        // 移除空白字符
        let trimmedText = text.trimmingCharacters(in: .whitespacesAndNewlines)
        if trimmedText.isEmpty { return 0 }
        
        var wordCount = 0
        
        // 遍历每个字符
        text.enumerateSubstrings(in: text.startIndex..<text.endIndex, options: [.byWords, .localized]) { (substring, _, _, _) in
            if substring != nil {
                wordCount += 1
            }
        }
        
        return wordCount
    }
    
    /// 检查富文本是否包含格式化内容
    /// - Parameter attributedString: 富文本
    /// - Returns: 是否包含格式化内容
    static func hasFormatting(_ attributedString: NSAttributedString) -> Bool {
        let fullRange = NSRange(location: 0, length: attributedString.length)
        var hasFormatting = false
        
        attributedString.enumerateAttributes(in: fullRange, options: []) { (attributes, _, stop) in
            // 检查是否有除了默认字体和颜色之外的属性
            if attributes.count > 2 {
                hasFormatting = true
                stop.pointee = true
            }
        }
        
        return hasFormatting
    }
    
    // MARK: - 性能优化相关
    
    /// 为大文本创建优化的NSAttributedString
    /// - Parameters:
    ///   - text: 文本内容
    ///   - font: 字体
    ///   - textColor: 文字颜色
    /// - Returns: 优化的富文本
    static func createOptimizedAttributedString(
        from text: String,
        font: UIFont = UIFont.systemFont(ofSize: 17),
        textColor: UIColor
    ) -> NSAttributedString {
        // 对于大文本，使用最少的属性以提高性能
        let attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: textColor
        ]
        
        return NSAttributedString(string: text, attributes: attributes)
    }
    
    /// 检查文本长度是否适合富文本处理
    /// - Parameter text: 文本内容
    /// - Returns: 是否适合富文本处理
    static func isTextSuitableForRichText(_ text: String) -> Bool {
        // 超过10000字符时建议谨慎使用复杂格式
        return text.count <= 10000
    }
}

// MARK: - 富文本样式预设

extension RichTextUtils {
    
    /// 预设的文本样式
    enum TextStyle {
        case title          // 标题样式
        case subtitle       // 副标题样式
        case body           // 正文样式
        case caption        // 说明文字样式
        case highlight      // 高亮样式
        
        /// 获取样式对应的字体
        var font: UIFont {
            switch self {
            case .title:
                return UIFont.boldSystemFont(ofSize: 24)
            case .subtitle:
                return UIFont.boldSystemFont(ofSize: 20)
            case .body:
                return UIFont.systemFont(ofSize: 17)
            case .caption:
                return UIFont.systemFont(ofSize: 14)
            case .highlight:
                return UIFont.boldSystemFont(ofSize: 17)
            }
        }
        
        /// 获取样式对应的颜色
        var color: UIColor {
            switch self {
            case .title, .subtitle:
                return UIColor.label
            case .body:
                return UIColor.label
            case .caption:
                return UIColor.secondaryLabel
            case .highlight:
                return UIColor.systemBlue
            }
        }
    }
    
    /// 应用预设样式到富文本
    /// - Parameters:
    ///   - attributedString: 可变富文本
    ///   - range: 要应用样式的范围
    ///   - style: 预设样式
    static func applyStyle(
        to attributedString: NSMutableAttributedString,
        range: NSRange,
        style: TextStyle
    ) {
        attributedString.addAttribute(.font, value: style.font, range: range)
        attributedString.addAttribute(.foregroundColor, value: style.color, range: range)
    }
}
