//
//  ScriptManager.swift
//  SmartTeleprompter
//
//  Created by ryan on 2025/7/18.
//

import Foundation
import SwiftData
import UIKit

// MARK: - 数据管理器

/// 统一的数据管理器
/// 整合文件夹管理器和笔记管理器，提供统一的数据操作接口
@Observable
final class ScriptManager {
    
    // MARK: - 属性
    
    /// SwiftData模型上下文
    let modelContext: ModelContext
    
    /// 文件夹管理器
    let folderManager: FolderManager
    
    /// 笔记管理器
    let noteManager: NoteManager
    
    // MARK: - 初始化
    
    /// 初始化数据管理器
    /// - Parameter modelContext: SwiftData模型上下文
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
        self.folderManager = FolderManager(modelContext: modelContext)
        self.noteManager = NoteManager(modelContext: modelContext, folderManager: folderManager)
        
        // 设置默认选中All Notes文件夹
        folderManager.selectAllNotesFolder()
    }
    
    // MARK: - 便捷访问属性
    
    /// 当前选中的文件夹
    var selectedFolder: Folder? {
        get { folderManager.selectedFolder }
        set { folderManager.selectFolder(newValue) }
    }
    
    /// 当前选中的笔记
    var selectedNote: Note? {
        get { noteManager.selectedNote }
        set { noteManager.selectNote(newValue) }
    }
    
    /// 当前排序类型
    var currentSortType: NoteSortType {
        get { noteManager.currentSortType }
        set { noteManager.setSortType(newValue, ascending: noteManager.isAscending) }
    }
    
    /// 是否升序排列
    var isAscending: Bool {
        get { noteManager.isAscending }
        set { noteManager.setSortType(noteManager.currentSortType, ascending: newValue) }
    }
    
    /// 搜索关键词
    var searchQuery: String {
        get { noteManager.searchQuery }
        set { noteManager.setSearchQuery(newValue) }
    }
    
    // MARK: - 文件夹操作
    
    /// 获取所有文件夹
    /// - Returns: 所有文件夹的数组
    func getAllFolders() -> [Folder] {
        return folderManager.getAllFolders()
    }
    
    /// 获取自定义文件夹
    /// - Returns: 自定义文件夹的数组
    func getCustomFolders() -> [Folder] {
        return folderManager.getCustomFolders()
    }
    
    /// 创建文件夹
    /// - Parameters:
    ///   - name: 文件夹名称
    ///   - icon: 文件夹图标，可选
    /// - Returns: 创建的文件夹
    @discardableResult
    func createFolder(name: String, icon: String? = nil) -> Folder {
        return folderManager.createFolder(name: name, icon: icon)
    }
    
    /// 删除文件夹
    /// - Parameter folder: 要删除的文件夹
    /// - Returns: 是否删除成功
    @discardableResult
    func deleteFolder(_ folder: Folder) -> Bool {
        return folderManager.deleteFolder(folder)
    }
    
    /// 重命名文件夹
    /// - Parameters:
    ///   - folder: 要重命名的文件夹
    ///   - newName: 新名称
    /// - Returns: 是否重命名成功
    @discardableResult
    func renameFolder(_ folder: Folder, to newName: String) -> Bool {
        return folderManager.renameFolder(folder, to: newName)
    }
    
    /// 更改文件夹图标
    /// - Parameters:
    ///   - folder: 要更改图标的文件夹
    ///   - newIcon: 新图标
    /// - Returns: 是否更改成功
    @discardableResult
    func changeFolderIcon(_ folder: Folder, to newIcon: String) -> Bool {
        return folderManager.changeIcon(of: folder, to: newIcon)
    }
    
    /// 获取All Notes文件夹
    /// - Returns: All Notes文件夹
    func getAllNotesFolder() -> Folder? {
        return folderManager.getAllNotesFolder()
    }
    
    /// 获取最近删除文件夹
    /// - Returns: 最近删除文件夹
    func getRecentlyDeletedFolder() -> Folder? {
        return folderManager.getRecentlyDeletedFolder()
    }
    
    // MARK: - 笔记操作
    
    /// 创建笔记
    /// - Parameters:
    ///   - title: 笔记标题
    ///   - content: 笔记内容
    ///   - folder: 所属文件夹，如果为nil则放入当前选中的文件夹或All Notes文件夹
    /// - Returns: 创建的笔记
    @discardableResult
    func createNote(
        title: String,
        content: String = "",
        folder: Folder? = nil
    ) -> Note {
        let targetFolder = folder ?? selectedFolder
        return noteManager.createNote(title: title, content: content, folder: targetFolder)
    }
    
    /// 创建富文本笔记
    /// - Parameters:
    ///   - title: 笔记标题
    ///   - richText: 富文本内容
    ///   - folder: 所属文件夹，如果为nil则放入当前选中的文件夹或All Notes文件夹
    /// - Returns: 创建的笔记
    @discardableResult
    func createRichTextNote(
        title: String,
        richText: NSAttributedString,
        folder: Folder? = nil
    ) -> Note {
        let targetFolder = folder ?? selectedFolder
        return noteManager.createRichTextNote(title: title, richText: richText, folder: targetFolder)
    }
    
    /// 从文本快速创建笔记
    /// - Parameters:
    ///   - text: 文本内容
    ///   - folder: 所属文件夹
    /// - Returns: 创建的笔记
    @discardableResult
    func createNoteFromText(
        _ text: String,
        folder: Folder? = nil
    ) -> Note {
        let targetFolder = folder ?? selectedFolder
        return noteManager.createNoteFromText(text, folder: targetFolder)
    }
    
    /// 删除笔记
    /// - Parameter note: 要删除的笔记
    func deleteNote(_ note: Note) {
        noteManager.deleteNote(note)
    }
    
    /// 永久删除笔记
    /// - Parameter note: 要永久删除的笔记
    func permanentlyDeleteNote(_ note: Note) {
        noteManager.permanentlyDeleteNote(note)
    }
    
    /// 恢复笔记
    /// - Parameters:
    ///   - note: 要恢复的笔记
    ///   - targetFolder: 目标文件夹
    func restoreNote(_ note: Note, to targetFolder: Folder? = nil) {
        noteManager.restoreNote(note, to: targetFolder)
    }
    
    /// 更新笔记
    /// - Parameters:
    ///   - note: 要更新的笔记
    ///   - title: 新标题
    ///   - content: 新内容
    func updateNote(_ note: Note, title: String? = nil, content: String? = nil) {
        noteManager.updateNote(note, title: title, content: content)
    }
    
    /// 更新笔记富文本内容
    /// - Parameters:
    ///   - note: 要更新的笔记
    ///   - richText: 新的富文本内容
    func updateNoteRichText(_ note: Note, richText: NSAttributedString) {
        noteManager.updateNoteRichText(note, richText: richText)
    }
    
    /// 切换笔记收藏状态
    /// - Parameter note: 要切换收藏状态的笔记
    func toggleNoteFavorite(_ note: Note) {
        noteManager.toggleNoteFavorite(note)
    }
    
    /// 移动笔记到指定文件夹
    /// - Parameters:
    ///   - note: 要移动的笔记
    ///   - targetFolder: 目标文件夹
    func moveNote(_ note: Note, to targetFolder: Folder) {
        noteManager.moveNote(note, to: targetFolder)
    }
    
    // MARK: - 查询操作
    
    /// 获取当前文件夹中的笔记
    /// - Returns: 当前文件夹中的笔记数组
    func getCurrentFolderNotes() -> [Note] {
        if let folder = selectedFolder {
            switch folder.type {
            case .recentlyDeleted:
                // 最近删除文件夹显示所有已删除的笔记
                return noteManager.getAllDeletedNotes()
            case .allNotes:
                // All Notes文件夹显示所有有效笔记
                return noteManager.getAllActiveNotes()
            case .custom:
                // 自定义文件夹显示该文件夹中的笔记
                return noteManager.getNotes(in: folder)
            }
        } else {
            // 没有选中文件夹时显示所有有效笔记
            return noteManager.getAllActiveNotes()
        }
    }
    
    /// 获取收藏的笔记
    /// - Returns: 收藏笔记数组
    func getFavoriteNotes() -> [Note] {
        return noteManager.getFavoriteNotes()
    }
    
    /// 搜索笔记
    /// - Parameter query: 搜索关键词
    /// - Returns: 匹配的笔记数组
    func searchNotes(query: String) -> [Note] {
        guard let selectedFolder = selectedFolder else {
            return noteManager.searchNotes(query: query, in: nil, includeDeleted: false)
        }

        switch selectedFolder.type {
        case .allNotes:
            return noteManager.searchNotes(query: query, in: nil, includeDeleted: false)
        case .recentlyDeleted:
            return noteManager.searchNotes(query: query, in: selectedFolder, includeDeleted: true)
        case .custom:
            return noteManager.searchNotes(query: query, in: selectedFolder, includeDeleted: false)
        }
    }
    
    /// 获取笔记
    /// - Parameter id: 笔记ID
    /// - Returns: 对应的笔记
    func getNote(by id: UUID) -> Note? {
        return noteManager.getNote(by: id)
    }
    
    /// 获取文件夹
    /// - Parameter id: 文件夹ID
    /// - Returns: 对应的文件夹
    func getFolder(by id: UUID) -> Folder? {
        return folderManager.getFolder(by: id)
    }
    
    // MARK: - 统计信息
    
    /// 获取总笔记数
    /// - Returns: 总笔记数
    func getTotalNoteCount() -> Int {
        return noteManager.getAllActiveNotes().count
    }
    
    /// 获取收藏笔记数
    /// - Returns: 收藏笔记数
    func getFavoriteNoteCount() -> Int {
        return noteManager.getFavoriteNotes().count
    }
    
    /// 获取已删除笔记数
    /// - Returns: 已删除笔记数
    func getDeletedNoteCount() -> Int {
        return noteManager.getAllDeletedNotes().count
    }
    
    /// 获取文件夹数
    /// - Returns: 文件夹数
    func getFolderCount() -> Int {
        return folderManager.getFolderCount()
    }
    
    /// 获取自定义文件夹数
    /// - Returns: 自定义文件夹数
    func getCustomFolderCount() -> Int {
        return folderManager.getCustomFolderCount()
    }
    
    // MARK: - 批量操作
    
    /// 批量删除笔记
    /// - Parameter notes: 要删除的笔记数组
    func deleteNotes(_ notes: [Note]) {
        for note in notes {
            noteManager.deleteNote(note)
        }
    }
    
    /// 批量恢复笔记
    /// - Parameters:
    ///   - notes: 要恢复的笔记数组
    ///   - targetFolder: 目标文件夹
    func restoreNotes(_ notes: [Note], to targetFolder: Folder? = nil) {
        for note in notes {
            noteManager.restoreNote(note, to: targetFolder)
        }
    }
    
    /// 批量移动笔记
    /// - Parameters:
    ///   - notes: 要移动的笔记数组
    ///   - targetFolder: 目标文件夹
    func moveNotes(_ notes: [Note], to targetFolder: Folder) {
        noteManager.moveNotes(notes, to: targetFolder)
    }
    
    /// 批量设置收藏状态
    /// - Parameters:
    ///   - notes: 要设置的笔记数组
    ///   - favorite: 是否收藏
    func setNotesFavorite(_ notes: [Note], favorite: Bool) {
        noteManager.setNotesFavorite(notes, favorite: favorite)
    }
    
    // MARK: - 清理操作
    
    /// 清理可永久删除的笔记（删除超过30天的笔记）
    /// - Returns: 清理的笔记数量
    @discardableResult
    func cleanupOldDeletedNotes() -> Int {
        let deletedNotes = noteManager.getAllDeletedNotes()
        let notesToCleanup = deletedNotes.filter { $0.canPermanentlyDelete() }
        
        for note in notesToCleanup {
            noteManager.permanentlyDeleteNote(note)
        }
        
        return notesToCleanup.count
    }
    
    /// 清空最近删除文件夹
    /// - Returns: 清理的笔记数量
    @discardableResult
    func emptyRecentlyDeleted() -> Int {
        let deletedNotes = noteManager.getAllDeletedNotes()
        
        for note in deletedNotes {
            noteManager.permanentlyDeleteNote(note)
        }
        
        return deletedNotes.count
    }
}
