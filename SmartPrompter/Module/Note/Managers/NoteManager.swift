//
//  NoteManager.swift
//  SmartTeleprompter
//
//  Created by ryan on 2025/7/18.
//

import Foundation
import SwiftData
import UIKit

// MARK: - 笔记排序类型

/// 笔记排序类型
enum NoteSortType: String, CaseIterable {
    case modifiedDate = "modified_date"     // 按修改时间排序
    case createdDate = "created_date"       // 按创建时间排序
    case title = "title"                    // 按标题排序
    case wordCount = "word_count"           // 按字数排序
    
    /// 排序显示名称
    var displayName: String {
        switch self {
        case .modifiedDate: "By Modified"
        case .createdDate: "By Created"
        case .title: "By Title"
        case .wordCount: "By Words"
        }
    }
    
    /// 排序显示名称
    var displayMenuName: String {
        switch self {
        case .modifiedDate: "Date Modified"
        case .createdDate: "Date Created"
        case .title: "Title A-Z"
        case .wordCount: "Word Count"
        }
    }
    
    /// 排序显示图标
    var displayIcon: String {
        switch self {
        case .modifiedDate: "clock"
        case .createdDate: "calendar.badge.plus"
        case .title: "textformat"
        case .wordCount: "textformat.123"
        }
    }
    
    var isTimeSort: Bool {
        return self == .modifiedDate || self == .createdDate
    }
}

enum NoteSortAscending: String, CaseIterable {
    case ascending = "ascending"     // 按修改时间排序
    case decline = "decline"       // 按创建时间排序
    
    /// 排序显示名称
    var displayName: String {
        switch self {
        case .ascending: "Newest First"
        case .decline: "Oldest First"
        }
    }
    
    /// 排序显示名称
    var displayAscendName: String {
        switch self {
        case .ascending: "Ascending"
        case .decline: "Descending"
        }
    }
}


// MARK: - 笔记管理器

/// 笔记管理器
/// 负责笔记的创建、删除、修改、搜索等操作
@Observable
final class NoteManager {
    
    // MARK: - 属性
    
    /// SwiftData模型上下文
    private let modelContext: ModelContext
    
    /// 文件夹管理器
    private let folderManager: FolderManager
    
    /// 当前选中的笔记
    var selectedNote: Note?
    
    /// 当前排序类型
    var currentSortType: NoteSortType = .modifiedDate
    
    /// 是否升序排列
    var isAscending: Bool = false
    
    /// 搜索关键词
    var searchQuery: String = ""
    
    /// 是否只显示收藏的笔记
    var showFavoritesOnly: Bool = false
    
    // MARK: - 初始化
    
    /// 初始化笔记管理器
    /// - Parameters:
    ///   - modelContext: SwiftData模型上下文
    ///   - folderManager: 文件夹管理器
    init(modelContext: ModelContext, folderManager: FolderManager) {
        self.modelContext = modelContext
        self.folderManager = folderManager
    }
    
    // MARK: - 笔记CRUD操作
    
    /// 创建新笔记
    /// - Parameters:
    ///   - title: 笔记标题
    ///   - content: 笔记内容（纯文本）
    ///   - folder: 所属文件夹，如果为nil则放入All Notes文件夹
    /// - Returns: 创建的笔记
    @discardableResult
    func createNote(
        title: String,
        content: String = "",
        folder: Folder? = nil
    ) -> Note {
        let targetFolder = folder ?? folderManager.getAllNotesFolder()
        let note = Note.createNote(title: title, content: content, folder: targetFolder)
        
        modelContext.insert(note)
        saveContext()
        
        return note
    }
    
    /// 创建富文本笔记
    /// - Parameters:
    ///   - title: 笔记标题
    ///   - richText: 富文本内容
    ///   - folder: 所属文件夹，如果为nil则放入All Notes文件夹
    /// - Returns: 创建的笔记
    @discardableResult
    func createRichTextNote(
        title: String,
        richText: NSAttributedString,
        folder: Folder? = nil
    ) -> Note {
        let targetFolder = folder ?? folderManager.getAllNotesFolder()
        let note = Note.createRichTextNote(title: title, richText: richText, folder: targetFolder)
        
        modelContext.insert(note)
        saveContext()
        
        return note
    }
    
    /// 从文本快速创建笔记
    /// - Parameters:
    ///   - text: 文本内容，第一行作为标题
    ///   - folder: 所属文件夹
    /// - Returns: 创建的笔记
    @discardableResult
    func createNoteFromText(
        _ text: String,
        folder: Folder? = nil
    ) -> Note {
        let targetFolder = folder ?? folderManager.getAllNotesFolder()
        let note = Note.createFromText(text, folder: targetFolder)
        
        modelContext.insert(note)
        saveContext()
        
        return note
    }
    
    /// 删除笔记（软删除）
    /// - Parameter note: 要删除的笔记
    func deleteNote(_ note: Note) {
        // 软删除笔记
        note.softDelete()
        
        // 移动到最近删除文件夹
        let recentlyDeletedFolder = folderManager.getRecentlyDeletedFolder()
        note.moveTo(folder: recentlyDeletedFolder)
        
        saveContext()
        
        // 如果删除的是当前选中的笔记，清除选中状态
        if selectedNote?.id == note.id {
            selectedNote = nil
        }
    }
    
    /// 永久删除笔记
    /// - Parameter note: 要永久删除的笔记
    func permanentlyDeleteNote(_ note: Note) {
        modelContext.delete(note)
        saveContext()
        
        // 如果删除的是当前选中的笔记，清除选中状态
        if selectedNote?.id == note.id {
            selectedNote = nil
        }
    }
    
    /// 恢复已删除的笔记
    /// - Parameters:
    ///   - note: 要恢复的笔记
    ///   - targetFolder: 目标文件夹，如果为nil则恢复到All Notes文件夹
    func restoreNote(_ note: Note, to targetFolder: Folder? = nil) {
        note.restore()
        
        let folder = targetFolder ?? folderManager.getAllNotesFolder()
        note.moveTo(folder: folder)
        
        saveContext()
    }
    
    /// 更新笔记内容
    /// - Parameters:
    ///   - note: 要更新的笔记
    ///   - title: 新标题，可选
    ///   - content: 新内容，可选
    func updateNote(_ note: Note, title: String? = nil, content: String? = nil) {
        note.updateInfo(title: title, content: content)
        saveContext()
    }
    
    /// 更新笔记富文本内容
    /// - Parameters:
    ///   - note: 要更新的笔记
    ///   - richText: 新的富文本内容
    func updateNoteRichText(_ note: Note, richText: NSAttributedString) {
        note.setRichText(richText)
        saveContext()
    }
    
    /// 清除笔记的富文本格式
    /// - Parameter note: 要清除格式的笔记
    func clearNoteRichTextFormatting(_ note: Note) {
        note.clearRichTextFormatting()
        saveContext()
    }
    
    // MARK: - 收藏操作
    
    /// 切换笔记收藏状态
    /// - Parameter note: 要切换收藏状态的笔记
    func toggleNoteFavorite(_ note: Note) {
        note.toggleFavorite()
        saveContext()
    }
    
    /// 设置笔记收藏状态
    /// - Parameters:
    ///   - note: 要设置的笔记
    ///   - favorite: 是否收藏
    func setNoteFavorite(_ note: Note, favorite: Bool) {
        note.setFavorite(favorite)
        saveContext()
    }
    
    /// 批量设置笔记收藏状态
    /// - Parameters:
    ///   - notes: 要设置的笔记数组
    ///   - favorite: 是否收藏
    func setNotesFavorite(_ notes: [Note], favorite: Bool) {
        for note in notes {
            note.setFavorite(favorite)
        }
        saveContext()
    }
    
    // MARK: - 移动操作
    
    /// 移动笔记到指定文件夹
    /// - Parameters:
    ///   - note: 要移动的笔记
    ///   - targetFolder: 目标文件夹
    func moveNote(_ note: Note, to targetFolder: Folder) {
        note.moveTo(folder: targetFolder)
        saveContext()
    }
    
    /// 批量移动笔记到指定文件夹
    /// - Parameters:
    ///   - notes: 要移动的笔记数组
    ///   - targetFolder: 目标文件夹
    func moveNotes(_ notes: [Note], to targetFolder: Folder) {
        for note in notes {
            note.moveTo(folder: targetFolder)
        }
        saveContext()
    }
    
    // MARK: - 笔记查询
    
    /// 获取指定文件夹中的笔记
    /// - Parameters:
    ///   - folder: 文件夹，如果为nil则获取所有笔记
    ///   - includeDeleted: 是否包含已删除的笔记，默认为false
    /// - Returns: 笔记数组
    func getNotes(in folder: Folder? = nil, includeDeleted: Bool = false) -> [Note] {
        var predicate: Predicate<Note>
        
        if let folder = folder {
            let folderId = folder.id
            if includeDeleted {
                predicate = #Predicate<Note> { note in
                    note.folder?.id == folderId
                }
            } else {
                predicate = #Predicate<Note> { note in
                    note.folder?.id == folderId && !note.isDeleted
                }
            }
        } else {
            if includeDeleted {
                predicate = #Predicate<Note> { _ in true }
            } else {
                predicate = #Predicate<Note> { note in
                    !note.isDeleted
                }
            }
        }
        
        let descriptor = FetchDescriptor<Note>(predicate: predicate)
        
        do {
            let notes = try modelContext.fetch(descriptor)
            return sortNotes(notes)
        } catch {
            print("获取笔记失败: \(error)")
            return []
        }
    }
    
    /// 获取所有有效笔记（未删除）
    /// - Returns: 有效笔记数组
    func getAllActiveNotes() -> [Note] {
        return getNotes(includeDeleted: false)
    }
    
    /// 获取所有已删除的笔记
    /// - Returns: 已删除笔记数组
    func getAllDeletedNotes() -> [Note] {
        let predicate = #Predicate<Note> { note in
            note.isDeleted
        }
        
        let descriptor = FetchDescriptor<Note>(predicate: predicate)
        
        do {
            let notes = try modelContext.fetch(descriptor)
            return sortNotes(notes)
        } catch {
            print("获取已删除笔记失败: \(error)")
            return []
        }
    }
    
    /// 获取收藏的笔记
    /// - Returns: 收藏笔记数组
    func getFavoriteNotes() -> [Note] {
        let predicate = #Predicate<Note> { note in
            note.isFavorite && !note.isDeleted
        }
        
        let descriptor = FetchDescriptor<Note>(predicate: predicate)
        
        do {
            let notes = try modelContext.fetch(descriptor)
            return sortNotes(notes)
        } catch {
            print("获取收藏笔记失败: \(error)")
            return []
        }
    }
    
    /// 根据ID查找笔记
    /// - Parameter id: 笔记ID
    /// - Returns: 对应的笔记，如果不存在返回nil
    func getNote(by id: UUID) -> Note? {
        let predicate = #Predicate<Note> { note in
            note.id == id
        }
        
        let descriptor = FetchDescriptor<Note>(predicate: predicate)
        
        do {
            let notes = try modelContext.fetch(descriptor)
            return notes.first
        } catch {
            print("根据ID查找笔记失败: \(error)")
            return nil
        }
    }
    
    // MARK: - 搜索功能
    
    /// 搜索笔记
    /// - Parameters:
    ///   - query: 搜索关键词
    ///   - folder: 限制搜索的文件夹，如果为nil则搜索所有文件夹
    ///   - includeDeleted: 是否包含已删除的笔记，默认为false
    /// - Returns: 匹配的笔记数组，按相关性排序
    func searchNotes(
        query: String,
        in folder: Folder? = nil,
        includeDeleted: Bool = false
    ) -> [Note] {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedQuery.isEmpty else { return [] }
        
        let notes = getNotes(in: folder, includeDeleted: includeDeleted)
        let matchingNotes = notes.filter { $0.matches(query: trimmedQuery) }
        
        // 按相关性排序
        return matchingNotes.sorted { note1, note2 in
            let score1 = note1.relevanceScore(for: trimmedQuery)
            let score2 = note2.relevanceScore(for: trimmedQuery)
            return score1 > score2
        }
    }
    
    /// 设置搜索查询
    /// - Parameter query: 搜索关键词
    func setSearchQuery(_ query: String) {
        searchQuery = query
    }
    
    /// 清除搜索查询
    func clearSearchQuery() {
        searchQuery = ""
    }
    
    // MARK: - 排序功能
    
    /// 设置排序类型
    /// - Parameters:
    ///   - sortType: 排序类型
    ///   - ascending: 是否升序，默认为false
    func setSortType(_ sortType: NoteSortType, ascending: Bool = false) {
        currentSortType = sortType
        isAscending = ascending
    }
    
    /// 切换排序顺序
    func toggleSortOrder() {
        isAscending.toggle()
    }
    
    /// 对笔记数组进行排序
    /// - Parameter notes: 要排序的笔记数组
    /// - Returns: 排序后的笔记数组
    private func sortNotes(_ notes: [Note]) -> [Note] {
        let sorted: [Note]
        
        switch currentSortType {
        case .modifiedDate:
            sorted = notes.sorted(by: Note.sortByModifiedDate)
        case .createdDate:
            sorted = notes.sorted(by: Note.sortByCreatedDate)
        case .title:
            sorted = notes.sorted(by: Note.sortByTitle)
        case .wordCount:
            sorted = notes.sorted(by: Note.sortByWordCount)
        }
        
        return isAscending ? sorted.reversed() : sorted
    }
    
    // MARK: - 选择管理
    
    /// 选择笔记
    /// - Parameter note: 要选择的笔记
    func selectNote(_ note: Note?) {
        selectedNote = note
    }
    
    // MARK: - 私有方法
    
    /// 保存上下文
    private func saveContext() {
        do {
            try modelContext.save()
        } catch {
            print("保存笔记数据失败: \(error)")
        }
    }
}
