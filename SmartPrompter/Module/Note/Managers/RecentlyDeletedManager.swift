//
//  RecentlyDeletedManager.swift
//  SmartTeleprompter
//
//  Created by ryan on 2025/7/18.
//

import Foundation
import SwiftData

// MARK: - 最近删除管理器

/// 最近删除管理器
/// 专门处理软删除、恢复、永久删除等操作
final class RecentlyDeletedManager {
    
    // MARK: - 常量
    
    /// 自动清理天数（30天）
    static let autoCleanupDays = 30
    
    /// 警告清理天数（7天）
    static let warningCleanupDays = 7
    
    // MARK: - 属性
    
    /// SwiftData模型上下文
    private let modelContext: ModelContext
    
    /// 文件夹管理器
    private let folderManager: FolderManager
    
    // MARK: - 初始化
    
    /// 初始化最近删除管理器
    /// - Parameters:
    ///   - modelContext: SwiftData模型上下文
    ///   - folderManager: 文件夹管理器
    init(modelContext: ModelContext, folderManager: FolderManager) {
        self.modelContext = modelContext
        self.folderManager = folderManager
    }
    
    // MARK: - 软删除操作
    
    /// 软删除笔记
    /// - Parameter note: 要删除的笔记
    func softDeleteNote(_ note: Note) {
        // 标记为已删除
        note.softDelete()
        
        // 移动到最近删除文件夹
        if let recentlyDeletedFolder = folderManager.getRecentlyDeletedFolder() {
            note.moveTo(folder: recentlyDeletedFolder)
        }
        
        saveContext()
    }
    
    /// 批量软删除笔记
    /// - Parameter notes: 要删除的笔记数组
    func softDeleteNotes(_ notes: [Note]) {
        let recentlyDeletedFolder = folderManager.getRecentlyDeletedFolder()
        
        for note in notes {
            note.softDelete()
            if let folder = recentlyDeletedFolder {
                note.moveTo(folder: folder)
            }
        }
        
        saveContext()
    }
    
    /// 软删除文件夹（将文件夹中的所有笔记移动到最近删除）
    /// - Parameter folder: 要删除的文件夹
    func softDeleteFolder(_ folder: Folder) {
        guard !folder.isDefault else {
            print("默认文件夹不能删除")
            return
        }
        
        let recentlyDeletedFolder = folderManager.getRecentlyDeletedFolder()
        
        // 将文件夹中的所有有效笔记移动到最近删除
        for note in folder.getActiveNotes() {
            note.softDelete()
            if let deletedFolder = recentlyDeletedFolder {
                note.moveTo(folder: deletedFolder)
            }
        }
        
        saveContext()
    }
    
    // MARK: - 恢复操作
    
    /// 恢复笔记
    /// - Parameters:
    ///   - note: 要恢复的笔记
    ///   - targetFolder: 目标文件夹，如果为nil则恢复到All Notes文件夹
    func restoreNote(_ note: Note, to targetFolder: Folder? = nil) {
        // 恢复笔记状态
        note.restore()
        
        // 移动到目标文件夹
        let folder = targetFolder ?? folderManager.getAllNotesFolder()
        note.moveTo(folder: folder)
        
        saveContext()
    }
    
    /// 批量恢复笔记
    /// - Parameters:
    ///   - notes: 要恢复的笔记数组
    ///   - targetFolder: 目标文件夹，如果为nil则恢复到All Notes文件夹
    func restoreNotes(_ notes: [Note], to targetFolder: Folder? = nil) {
        let folder = targetFolder ?? folderManager.getAllNotesFolder()
        
        for note in notes {
            note.restore()
            note.moveTo(folder: folder)
        }
        
        saveContext()
    }
    
    /// 恢复所有已删除的笔记
    /// - Parameter targetFolder: 目标文件夹，如果为nil则恢复到All Notes文件夹
    func restoreAllNotes(to targetFolder: Folder? = nil) {
        let deletedNotes = getAllDeletedNotes()
        restoreNotes(deletedNotes, to: targetFolder)
    }
    
    // MARK: - 永久删除操作
    
    /// 永久删除笔记
    /// - Parameter note: 要永久删除的笔记
    func permanentlyDeleteNote(_ note: Note) {
        modelContext.delete(note)
        saveContext()
    }
    
    /// 批量永久删除笔记
    /// - Parameter notes: 要永久删除的笔记数组
    func permanentlyDeleteNotes(_ notes: [Note]) {
        for note in notes {
            modelContext.delete(note)
        }
        saveContext()
    }
    
    /// 清空最近删除文件夹
    /// - Returns: 清理的笔记数量
    @discardableResult
    func emptyRecentlyDeleted() -> Int {
        let deletedNotes = getAllDeletedNotes()
        
        for note in deletedNotes {
            modelContext.delete(note)
        }
        
        saveContext()
        return deletedNotes.count
    }
    
    // MARK: - 自动清理
    
    /// 自动清理过期的已删除笔记
    /// - Returns: 清理的笔记数量
    @discardableResult
    func autoCleanupExpiredNotes() -> Int {
        let expiredNotes = getExpiredDeletedNotes()
        
        for note in expiredNotes {
            modelContext.delete(note)
        }
        
        saveContext()
        return expiredNotes.count
    }
    
    /// 获取即将过期的笔记（7天内将被自动删除）
    /// - Returns: 即将过期的笔记数组
    func getNotesNearExpiration() -> [Note] {
        let deletedNotes = getAllDeletedNotes()
        let warningDate = Calendar.current.date(byAdding: .day, value: -Self.warningCleanupDays, to: Date()) ?? Date()
        
        return deletedNotes.filter { note in
            guard let deletedAt = note.deletedAt else { return false }
            return deletedAt < warningDate && !note.canPermanentlyDelete()
        }
    }
    
    /// 获取已过期的笔记（超过30天）
    /// - Returns: 已过期的笔记数组
    func getExpiredDeletedNotes() -> [Note] {
        let deletedNotes = getAllDeletedNotes()
        return deletedNotes.filter { $0.canPermanentlyDelete() }
    }
    
    // MARK: - 查询操作
    
    /// 获取所有已删除的笔记
    /// - Returns: 已删除笔记数组
    func getAllDeletedNotes() -> [Note] {
        let predicate = #Predicate<Note> { note in
            note.isDeleted
        }
        
        let descriptor = FetchDescriptor<Note>(
            predicate: predicate,
            sortBy: [SortDescriptor(\.deletedAt, order: .reverse)]
        )
        
        do {
            return try modelContext.fetch(descriptor)
        } catch {
            print("获取已删除笔记失败: \(error)")
            return []
        }
    }
    
    /// 获取指定日期范围内删除的笔记
    /// - Parameters:
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 指定日期范围内删除的笔记数组
    func getDeletedNotes(from startDate: Date, to endDate: Date) -> [Note] {
        let predicate = #Predicate<Note> { note in
            note.isDeleted && 
            note.deletedAt != nil &&
            note.deletedAt! >= startDate &&
            note.deletedAt! <= endDate
        }
        
        let descriptor = FetchDescriptor<Note>(
            predicate: predicate,
            sortBy: [SortDescriptor(\.deletedAt, order: .reverse)]
        )
        
        do {
            return try modelContext.fetch(descriptor)
        } catch {
            print("获取指定日期范围内删除的笔记失败: \(error)")
            return []
        }
    }
    
    /// 获取今天删除的笔记
    /// - Returns: 今天删除的笔记数组
    func getTodayDeletedNotes() -> [Note] {
        let calendar = Calendar.current
        let today = Date()
        let startOfDay = calendar.startOfDay(for: today)
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay) ?? today
        
        return getDeletedNotes(from: startOfDay, to: endOfDay)
    }
    
    /// 获取本周删除的笔记
    /// - Returns: 本周删除的笔记数组
    func getThisWeekDeletedNotes() -> [Note] {
        let calendar = Calendar.current
        let today = Date()
        let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: today)?.start ?? today
        let endOfWeek = calendar.dateInterval(of: .weekOfYear, for: today)?.end ?? today
        
        return getDeletedNotes(from: startOfWeek, to: endOfWeek)
    }
    
    // MARK: - 统计信息
    
    /// 获取已删除笔记总数
    /// - Returns: 已删除笔记总数
    func getDeletedNoteCount() -> Int {
        return getAllDeletedNotes().count
    }
    
    /// 获取即将过期的笔记数量
    /// - Returns: 即将过期的笔记数量
    func getNotesNearExpirationCount() -> Int {
        return getNotesNearExpiration().count
    }
    
    /// 获取已过期的笔记数量
    /// - Returns: 已过期的笔记数量
    func getExpiredNotesCount() -> Int {
        return getExpiredDeletedNotes().count
    }
    
    /// 获取最近删除文件夹的存储大小估算
    /// - Returns: 存储大小估算（字符数）
    func getStorageSizeEstimate() -> Int {
        let deletedNotes = getAllDeletedNotes()
        return deletedNotes.reduce(0) { total, note in
            total + note.characterCount
        }
    }
    
    // MARK: - 工具方法
    
    /// 检查笔记是否在最近删除文件夹中
    /// - Parameter note: 要检查的笔记
    /// - Returns: 是否在最近删除文件夹中
    func isNoteInRecentlyDeleted(_ note: Note) -> Bool {
        guard let recentlyDeletedFolder = folderManager.getRecentlyDeletedFolder() else {
            return false
        }
        return note.folder?.id == recentlyDeletedFolder.id && note.isDeleted
    }
    
    /// 获取笔记的删除天数
    /// - Parameter note: 笔记
    /// - Returns: 删除天数，如果笔记未删除返回nil
    func getDaysSinceDeleted(_ note: Note) -> Int? {
        guard let deletedAt = note.deletedAt else { return nil }
        let calendar = Calendar.current
        let days = calendar.dateComponents([.day], from: deletedAt, to: Date()).day
        return days
    }
    
    /// 获取笔记距离永久删除的剩余天数
    /// - Parameter note: 笔记
    /// - Returns: 剩余天数，如果笔记未删除或已过期返回nil
    func getDaysUntilPermanentDeletion(_ note: Note) -> Int? {
        guard let daysSinceDeleted = getDaysSinceDeleted(note) else { return nil }
        let remainingDays = Self.autoCleanupDays - daysSinceDeleted
        return remainingDays > 0 ? remainingDays : nil
    }
    
    // MARK: - 私有方法
    
    /// 保存上下文
    private func saveContext() {
        do {
            try modelContext.save()
        } catch {
            print("保存最近删除数据失败: \(error)")
        }
    }
}
