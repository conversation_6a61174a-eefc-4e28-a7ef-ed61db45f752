//
//  FolderManager.swift
//  SmartTeleprompter
//
//  Created by ryan on 2025/7/18.
//

import Foundation
import SwiftData

// MARK: - 文件夹管理器

/// 文件夹管理器
/// 负责文件夹的创建、删除、修改等操作，以及默认文件夹的管理
@Observable
final class FolderManager {
    
    // MARK: - 属性
    
    /// SwiftData模型上下文
    private let modelContext: ModelContext
    
    /// 当前选中的文件夹
    var selectedFolder: Folder?
    
    /// 所有文件夹的缓存
    private var foldersCache: [Folder] = []
    
    /// 缓存是否有效
    private var isCacheValid = false
    
    // MARK: - 初始化
    
    /// 初始化文件夹管理器
    /// - Parameter modelContext: SwiftData模型上下文
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
        setupDefaultFolders()
    }
    
    // MARK: - 默认文件夹管理
    
    /// 设置默认文件夹
    /// 确保"All Notes"和"最近删除"文件夹存在
    private func setupDefaultFolders() {
        // 检查是否已存在默认文件夹
        let allNotesFolder = getFolder(type: .allNotes)
        let recentlyDeletedFolder = getFolder(type: .recentlyDeleted)
        
        // 如果不存在，则创建
        if allNotesFolder == nil {
            let folder = Folder.createAllNotesFolder()
            modelContext.insert(folder)
        }
        
        if recentlyDeletedFolder == nil {
            let folder = Folder.createRecentlyDeletedFolder()
            modelContext.insert(folder)
        }
        
        saveContext()
        invalidateCache()
    }
    
    /// 获取指定类型的文件夹
    /// - Parameter type: 文件夹类型
    /// - Returns: 对应类型的文件夹，如果不存在返回nil
    func getFolder(type: FolderType) -> Folder? {
        // 获取所有文件夹，然后在内存中过滤
        let descriptor = FetchDescriptor<Folder>()

        do {
            let folders = try modelContext.fetch(descriptor)
            return folders.first { $0.type == type }
        } catch {
            print("获取\(type)文件夹失败: \(error)")
            return nil
        }
    }
    
    /// 获取All Notes文件夹
    /// - Returns: All Notes文件夹
    func getAllNotesFolder() -> Folder? {
        return getFolder(type: .allNotes)
    }
    
    /// 获取最近删除文件夹
    /// - Returns: 最近删除文件夹
    func getRecentlyDeletedFolder() -> Folder? {
        return getFolder(type: .recentlyDeleted)
    }
    
    // MARK: - 文件夹CRUD操作
    
    /// 创建自定义文件夹
    /// - Parameters:
    ///   - name: 文件夹名称
    ///   - icon: 文件夹图标，可选，默认使用"folder"
    /// - Returns: 创建的文件夹
    @discardableResult
    func createFolder(name: String, icon: String? = nil) -> Folder {
        let folder = Folder.createCustomFolder(
            name: name,
            icon: icon,
            sortOrder: getNextSortOrder()
        )
        
        modelContext.insert(folder)
        saveContext()
        invalidateCache()
        
        return folder
    }
    
    /// 删除文件夹
    /// - Parameter folder: 要删除的文件夹
    /// - Returns: 是否删除成功
    @discardableResult
    func deleteFolder(_ folder: Folder) -> Bool {
        // 默认文件夹不能删除
        guard !folder.isDefault else {
            print("默认文件夹不能删除")
            return false
        }
        
        // 将文件夹中的笔记移动到最近删除文件夹
        let recentlyDeletedFolder = getRecentlyDeletedFolder()
        for note in folder.getActiveNotes() {
            note.softDelete()
            note.moveTo(folder: recentlyDeletedFolder)
        }
        
        // 删除文件夹
        modelContext.delete(folder)
        saveContext()
        invalidateCache()
        
        // 如果删除的是当前选中的文件夹，重置选中状态
        if selectedFolder?.id == folder.id {
            selectedFolder = getAllNotesFolder()
        }
        
        return true
    }
    
    /// 重命名文件夹
    /// - Parameters:
    ///   - folder: 要重命名的文件夹
    ///   - newName: 新名称
    /// - Returns: 是否重命名成功
    @discardableResult
    func renameFolder(_ folder: Folder, to newName: String) -> Bool {
        guard !folder.isDefault else {
            print("默认文件夹不能重命名")
            return false
        }
        
        guard !newName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            print("文件夹名称不能为空")
            return false
        }
        
        let success = folder.updateInfo(name: newName)
        if success {
            saveContext()
            invalidateCache()
        }
        
        return success
    }
    
    /// 更改文件夹图标
    /// - Parameters:
    ///   - folder: 要更改图标的文件夹
    ///   - newIcon: 新图标
    /// - Returns: 是否更改成功
    @discardableResult
    func changeIcon(of folder: Folder, to newIcon: String) -> Bool {
        guard !folder.isDefault else {
            print("默认文件夹不能更改图标")
            return false
        }
        
        let success = folder.updateInfo(icon: newIcon)
        if success {
            saveContext()
            invalidateCache()
        }
        
        return success
    }
    
    /// 更新文件夹信息
    /// - Parameters:
    ///   - folder: 要更新的文件夹
    ///   - name: 新名称，可选
    ///   - icon: 新图标，可选
    /// - Returns: 是否更新成功
    @discardableResult
    func updateFolder(_ folder: Folder, name: String? = nil, icon: String? = nil) -> Bool {
        guard !folder.isDefault else {
            print("默认文件夹不能修改")
            return false
        }
        
        let success = folder.updateInfo(name: name, icon: icon)
        if success {
            saveContext()
            invalidateCache()
        }
        
        return success
    }
    
    // MARK: - 文件夹查询
    
    /// 获取所有文件夹
    /// - Returns: 所有文件夹的数组，按排序权重排序
    func getAllFolders() -> [Folder] {
        if isCacheValid {
            return foldersCache
        }
        
        let descriptor = FetchDescriptor<Folder>(
            sortBy: [SortDescriptor(\.sortOrder), SortDescriptor(\.createdAt)]
        )
        
        do {
            let folders = try modelContext.fetch(descriptor)
            foldersCache = folders
            isCacheValid = true
            return folders
        } catch {
            print("获取所有文件夹失败: \(error)")
            return []
        }
    }
    
    /// 获取自定义文件夹
    /// - Returns: 自定义文件夹的数组
    func getCustomFolders() -> [Folder] {
        return getAllFolders().filter { $0.type == .custom }
    }
    
    /// 获取默认文件夹
    /// - Returns: 默认文件夹的数组
    func getDefaultFolders() -> [Folder] {
        return getAllFolders().filter { $0.isDefault }
    }
    
    /// 根据ID查找文件夹
    /// - Parameter id: 文件夹ID
    /// - Returns: 对应的文件夹，如果不存在返回nil
    func getFolder(by id: UUID) -> Folder? {
        return getAllFolders().first { $0.id == id }
    }
    
    /// 根据名称查找文件夹
    /// - Parameter name: 文件夹名称
    /// - Returns: 对应的文件夹，如果不存在返回nil
    func getFolder(by name: String) -> Folder? {
        return getAllFolders().first { $0.name == name }
    }
    
    /// 检查文件夹名称是否已存在
    /// - Parameter name: 要检查的名称
    /// - Returns: 是否已存在
    func isFolderNameExists(_ name: String) -> Bool {
        return getFolder(by: name) != nil
    }
    
    // MARK: - 文件夹统计
    
    /// 获取文件夹总数
    /// - Returns: 文件夹总数
    func getFolderCount() -> Int {
        return getAllFolders().count
    }
    
    /// 获取自定义文件夹数量
    /// - Returns: 自定义文件夹数量
    func getCustomFolderCount() -> Int {
        return getCustomFolders().count
    }
    
    /// 获取包含笔记的文件夹数量
    /// - Returns: 包含笔记的文件夹数量
    func getFoldersWithNotesCount() -> Int {
        return getAllFolders().filter { $0.noteCount > 0 }.count
    }
    
    // MARK: - 排序管理
    
    /// 获取下一个排序权重
    /// - Returns: 下一个排序权重
    private func getNextSortOrder() -> Int {
        let customFolders = getCustomFolders()
        let maxSortOrder = customFolders.map { $0.sortOrder }.max() ?? 0
        return maxSortOrder + 1
    }
    
    /// 重新排序文件夹
    /// - Parameter folders: 重新排序后的文件夹数组
    func reorderFolders(_ folders: [Folder]) {
        for (index, folder) in folders.enumerated() {
            folder.sortOrder = index
        }
        saveContext()
        invalidateCache()
    }
    
    // MARK: - 选择管理
    
    /// 选择文件夹
    /// - Parameter folder: 要选择的文件夹
    func selectFolder(_ folder: Folder?) {
        selectedFolder = folder
    }
    
    /// 选择All Notes文件夹
    func selectAllNotesFolder() {
        selectedFolder = getAllNotesFolder()
    }
    
    /// 选择最近删除文件夹
    func selectRecentlyDeletedFolder() {
        selectedFolder = getRecentlyDeletedFolder()
    }
    
    // MARK: - 私有方法
    
    /// 保存上下文
    private func saveContext() {
        do {
            try modelContext.save()
        } catch {
            print("保存文件夹数据失败: \(error)")
        }
    }
    
    /// 使缓存失效
    private func invalidateCache() {
        isCacheValid = false
    }
}
