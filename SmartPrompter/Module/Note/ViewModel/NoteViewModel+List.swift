//
//  NoteViewModel+List.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/20.
//

import Foundation

// MARK: - 管理列表显示
extension NoteViewModel {
    /// 获取显示的笔记列表（搜索结果或全部笔记）
    var displayedNotes: [Note] {
        if isSearching {
            return searchResults
        }
        // 1、检出当前文件夹包含的笔记
        let currentFolderNotes = checkOoutCurrentFolderNotes()
        // 2、排序
        let sortedNotes = sortNotes(currentFolderNotes)
        // 3、过滤是否选择了喜欢
        if isInlikeMode {
            return sortedNotes.filter { $0.isFavorite }
        }
        return sortedNotes
    }
    
    // 检出当前文件夹包含的笔记
    private func checkOoutCurrentFolderNotes() -> [Note] {
        var currentFolderNotes: [Note]
        switch selectedFolder?.type {
        case nil: currentFolderNotes = notes
        case .allNotes: currentFolderNotes = notes
        case .custom:
            currentFolderNotes = notes.filter { $0.folder == selectedFolder }
        case .recentlyDeleted:
            currentFolderNotes = notes.filter { $0.folder?.isDeleted == true }
        }
        return currentFolderNotes
    }
}

extension NoteViewModel {

    /// 对笔记进行排序
    private func sortNotes(_ notes: [Note]) -> [Note] {
        let sorted: [Note]

        switch noteSortMode {
        case .modifiedDate:
            sorted = notes.sorted { $0.modifiedAt > $1.modifiedAt }
        case .createdDate:
            sorted = notes.sorted { $0.createdAt > $1.createdAt }
        case .title:
            sorted = notes.sorted { $0.title.localizedCaseInsensitiveCompare($1.title) == .orderedAscending }
        case .wordCount:
            sorted = notes.sorted { $0.content.count < $1.content.count }
        }

        return noteAscending == .ascending ? sorted : sorted.reversed()
    }

}
