//
//  NoteViewModel.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/19.
//

import SwiftData
import SwiftUI
import Combine

/// 笔记视图的 ViewModel
/// 负责管理笔记列表的状态和业务逻辑
@Observable
class NoteViewModel {

    // MARK: - 属性

    /// ScriptManager 实例，用于数据操作
    var scriptManager: ScriptManager?

    /// 这是存在的所有笔记
    var notes: [Note] = []
    /// 所有文件夹
    var folders: [Folder] = []
    /// 可移动的文件夹
    var canMoveToFolders: [Folder] {
        return folders.filter { !$0.type.isDefault }
    }
    
    /// 当前选中的笔记
    var selectedNote: Note?
    /// 当前选中的文件夹
    var selectedFolder: Folder?
    /// 当前menu显示的文件夹名
    var menuFolderName: String {
        if let selectedFolder = selectedFolder, selectedFolder.isDefault {
            return "Folders"
        }
        return selectedFolder?.name ?? "Folders"
    }
    /// 当前menu需要显示的文件夹
    var menuFolders: [Folder] {
        return folders.filter { !$0.type.isDefault }
    }
    
    /// 当前是否是筛选like
    var isInlikeMode: Bool = false
    /// 当前note的排序方式
    var noteSortMode: NoteSortType = .createdDate
    /// 是否升序排列
    var noteAscending: NoteSortAscending = .ascending
    /// 搜索关键词
    var searchText: String = "" {
        didSet {
            if searchText != oldValue {
                performSearch()
            }
        }
    }
    /// 是否显示搜索结果
    var isSearching: Bool {
        return !searchText.isEmpty
    }
    /// 搜索结果
    var searchResults: [Note] = []
    
    // MARK: - UI相关状态
    var showEditView: Bool = false

    // 增加数据监听绑定
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化

    /// 初始化 ViewModel
    /// - Parameter scriptManager: ScriptManager 实例
    init(scriptManager: ScriptManager? = nil) {
        self.scriptManager = scriptManager
    }

    /// 设置 ScriptManager
    /// - Parameter scriptManager: ScriptManager 实例
    func setScriptManager(_ scriptManager: ScriptManager) {
        self.scriptManager = scriptManager
        requestAllNotesAndFolder()
    }
}

// MARK: - 数据操作
extension NoteViewModel {

    /// 请求所有笔记和文件夹
    func requestAllNotesAndFolder() {
        guard let scriptManager = scriptManager else {
            return
        }
        self.folders = scriptManager.getAllFolders()
        self.notes = scriptManager.noteManager.getAllActiveNotes()
        
        // 默认选中全部文件夹
        self.selectedFolder = scriptManager.getAllNotesFolder()
    }
    
    /// 执行数据操作并自动保存刷新
    /// - Parameter operation: 要执行的数据操作
    func performDataOperation(_ operation: () -> Void) {
        operation()
        // 保存到SwiftData
        try? scriptManager?.modelContext.save()
        // 刷新数据
        requestAllNotesAndFolder()
    }
}

// MARK: - 搜索功能
extension NoteViewModel {

    /// 执行搜索
    private func performSearch() {
        guard let scriptManager = scriptManager else { return }

        if searchText.isEmpty {
            searchResults = []
            return
        }

        searchResults = scriptManager.searchNotes(query: searchText)
    }

    /// 清除搜索
    func clearSearch() {
        searchText = ""
        searchResults = []
    }
}

