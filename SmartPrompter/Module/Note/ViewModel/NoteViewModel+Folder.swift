//
//  NoteViewModel+Folder.swift
//  SmartPrompter
//
//  Created by 郭炜 on 2025/7/22.
//

import Foundation

// MARK: - 文件夹管理
extension NoteViewModel {
    
    /// 添加文件夹
    func addFolder(folderName: String) -> Folder? {
        let folder = scriptManager?.folderManager.createFolder(name: folderName)
        self.folders = scriptManager?.getAllFolders() ?? []
        return folder
    }
    
    /// 选中所有文件的文件夹
    func selectAllNotesFolder() {
        self.selectedFolder = scriptManager?.folderManager.getAllNotesFolder()
    }
}

extension NoteViewModel {
    /// 添加文件夹的弹窗
    func addFolderAlert() {
        // 添加文件夹
        AppMenuManager.showTextInputAlert(title: "New Folder",
                                          message: nil,
                                          placeholder: "Folder name",
                                          defaultText: nil,
                                          confirmTitle: "Save",
                                          cancelTitle: "Cancel",
                                          keyboardType: .default) { folderName in
            if let folder = self.addFolder(folderName: folderName) {
                self.selectedFolder = folder
            }
        }
    }
}
