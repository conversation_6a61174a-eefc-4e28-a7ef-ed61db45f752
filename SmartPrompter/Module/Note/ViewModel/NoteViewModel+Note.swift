//
//  NoteViewModel+Add.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/20.
//

import Foundation

// MARK: - 添加、编辑台本
extension NoteViewModel {

    /// 添加台本
    func addNote(note: Note) {
        performDataOperation {
            scriptManager?.selectedFolder?.addNote(note)
        }
    }

    /// 编辑台本富文本内容
    /// - Parameters:
    ///   - note: 要编辑的台本
    ///   - richText: 新的富文本内容
    func updateNoteRichText(_ note: Note) {
        performDataOperation { }
    }

    /// 移动笔记到文件夹
    func moveNote(_ note: Note, to folder: Folder) {
        performDataOperation {
            note.moveTo(folder: folder)
        }
    }

    /// 切换笔记收藏状态
    func toggleNoteFavorite(_ note: Note) {
        performDataOperation {
            note.toggleFavorite()
        }
    }

    /// 删除笔记
    func deleteNote(_ note: Note) {
        performDataOperation {
            scriptManager?.noteManager.deleteNote(note)
        }
    }

}
