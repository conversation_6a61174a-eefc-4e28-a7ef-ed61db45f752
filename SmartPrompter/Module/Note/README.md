# 智能提词器 - 数据模型与管理器

这是智能提词器应用的核心数据管理模块，提供完整的文件夹和笔记管理功能。**此版本仅包含数据模型和管理器，不包含UI界面。**

## 🎯 核心特性

### 📁 文件夹管理
- **默认文件夹**: All Notes（所有笔记）、最近删除（不可删除、重命名）
- **自定义文件夹**: 支持创建、重命名、删除、修改图标
- **层级管理**: 完整的文件夹组织结构

### 📝 笔记管理
- **CRUD操作**: 创建、读取、更新、删除笔记
- **富文本支持**: 基于NSAttributedString的原生富文本处理
- **收藏功能**: 笔记收藏和取消收藏
- **移动功能**: 在文件夹间移动笔记

### 🗑️ 最近删除
- **软删除机制**: 删除的笔记进入最近删除文件夹
- **自动清理**: 30天后自动永久删除
- **批量操作**: 支持批量恢复和永久删除

### 🎨 富文本支持
- **样式**: 加粗、斜体、下划线、删除线
- **颜色**: 文字颜色和背景颜色
- **字体**: 可调节字体大小
- **性能优化**: 针对大文本（1万字+）的性能优化

## 🏗️ 技术架构

### 数据层
- **SwiftData**: iOS 17+ 现代化数据持久化框架
- **关系模型**: Note ↔ Folder 多对一关系
- **富文本存储**: NSAttributedString序列化存储

### 管理层
- **ScriptManager**: 统一数据管理器
- **FolderManager**: 文件夹专用管理器
- **NoteManager**: 笔记专用管理器
- **RecentlyDeletedManager**: 最近删除专用管理器

### 工具层
- **RichTextUtils**: 富文本处理工具
- **原子化操作**: 所有操作都是最小原子化的

## 📊 数据模型

### Folder（文件夹）
```swift
@Model
final class Folder {
    var id: UUID                    // 唯一标识
    var name: String               // 文件夹名称
    var icon: String               // SF Symbol图标名
    var type: FolderType           // 文件夹类型（默认/自定义）
    var createdAt: Date            // 创建时间
    var modifiedAt: Date           // 修改时间
    var sortOrder: Int             // 排序权重
    var notes: [Note]              // 包含的笔记
}
```

### Note（笔记）
```swift
@Model
final class Note {
    var id: UUID                   // 唯一标识
    var title: String              // 笔记标题
    var content: String            // 纯文本内容（兼容性）
    var richTextData: Data?        // 富文本数据（序列化）
    var createdAt: Date            // 创建时间
    var modifiedAt: Date           // 修改时间
    var isFavorite: Bool           // 是否收藏
    var isDeleted: Bool            // 是否已删除（软删除）
    var deletedAt: Date?           // 删除时间
    var sortOrder: Int             // 排序权重
    var folder: Folder?            // 所属文件夹
}
```

### FolderType（文件夹类型）
```swift
enum FolderType: String, Codable {
    case allNotes = "all_notes"           // 所有笔记
    case recentlyDeleted = "recently_deleted"  // 最近删除
    case custom = "custom"                // 自定义文件夹
}
```

## 🔧 API 接口

### ScriptManager（统一管理器）

#### 初始化
```swift
let scriptManager = ScriptManager(modelContext: modelContext)
```

#### 文件夹操作
```swift
// 获取所有文件夹
let folders = scriptManager.getAllFolders()

// 创建文件夹
let folder = scriptManager.createFolder(name: "我的文件夹", icon: "folder")

// 重命名文件夹
scriptManager.renameFolder(folder, to: "新名称")

// 删除文件夹
scriptManager.deleteFolder(folder)

// 更改文件夹图标
scriptManager.changeFolderIcon(folder, to: "folder.fill")
```

#### 笔记操作
```swift
// 创建笔记
let note = scriptManager.createNote(title: "标题", content: "内容", folder: folder)

// 创建富文本笔记
let richText = NSAttributedString(string: "富文本内容")
let note = scriptManager.createRichTextNote(title: "标题", richText: richText, folder: folder)

// 更新笔记
scriptManager.updateNote(note, title: "新标题", content: "新内容")

// 更新富文本
scriptManager.updateNoteRichText(note, richText: newRichText)

// 收藏/取消收藏
scriptManager.toggleNoteFavorite(note)

// 移动笔记
scriptManager.moveNote(note, to: targetFolder)

// 删除笔记（软删除）
scriptManager.deleteNote(note)

// 永久删除笔记
scriptManager.permanentlyDeleteNote(note)

// 恢复笔记
scriptManager.restoreNote(note, to: targetFolder)
```

#### 查询操作
```swift
// 获取当前文件夹的笔记
let notes = scriptManager.getCurrentFolderNotes()

// 获取收藏笔记
let favorites = scriptManager.getFavoriteNotes()

// 搜索笔记
let results = scriptManager.searchNotes(query: "关键词")

// 获取统计信息
let totalNotes = scriptManager.getTotalNoteCount()
let favoriteCount = scriptManager.getFavoriteNoteCount()
let deletedCount = scriptManager.getDeletedNoteCount()
```

#### 批量操作
```swift
// 批量删除
scriptManager.deleteNotes([note1, note2, note3])

// 批量恢复
scriptManager.restoreNotes([note1, note2], to: targetFolder)

// 批量移动
scriptManager.moveNotes([note1, note2], to: targetFolder)

// 批量设置收藏
scriptManager.setNotesFavorite([note1, note2], favorite: true)
```

### RichTextUtils（富文本工具）

#### 基础操作
```swift
// 序列化富文本
let data = RichTextUtils.serialize(attributedString)

// 反序列化富文本
let attributedString = RichTextUtils.deserialize(data)

// 从纯文本创建富文本
let richText = RichTextUtils.createAttributedString(from: "文本")
```

#### 样式操作
```swift
let mutableText = NSMutableAttributedString(string: "示例文本")
let range = NSRange(location: 0, length: 2)

// 添加加粗
RichTextUtils.addBoldStyle(to: mutableText, range: range)

// 添加颜色
RichTextUtils.setTextColor(to: mutableText, range: range, color: .red)

// 添加背景色
RichTextUtils.setBackgroundColor(to: mutableText, range: range, color: .yellow)

// 设置字体大小
RichTextUtils.setFontSize(to: mutableText, range: range, fontSize: 20)
```

#### 预设样式
```swift
// 应用预设样式
RichTextUtils.applyStyle(to: mutableText, range: range, style: .title)
RichTextUtils.applyStyle(to: mutableText, range: range, style: .highlight)
```

## 📱 使用示例

### 基础使用流程
```swift
import SwiftUI
import SwiftData

struct ContentView: View {
    @Environment(\.modelContext) private var modelContext
    @State private var scriptManager: ScriptManager?
    
    var body: some View {
        VStack {
            if let manager = scriptManager {
                // 显示统计信息
                Text("文件夹: \(manager.getFolderCount())")
                Text("笔记: \(manager.getTotalNoteCount())")
                
                Button("创建示例数据") {
                    createSampleData(manager)
                }
            }
        }
        .onAppear {
            scriptManager = ScriptManager(modelContext: modelContext)
        }
    }
    
    func createSampleData(_ manager: ScriptManager) {
        // 创建文件夹
        let workFolder = manager.createFolder(name: "工作", icon: "briefcase")
        let personalFolder = manager.createFolder(name: "个人", icon: "person")
        
        // 创建笔记
        let note1 = manager.createNote(
            title: "会议记录",
            content: "今天的会议内容...",
            folder: workFolder
        )
        
        // 创建富文本笔记
        let richText = NSMutableAttributedString(string: "重要提醒")
        RichTextUtils.addBoldStyle(to: richText, range: NSRange(location: 0, length: 4))
        RichTextUtils.setTextColor(to: richText, range: NSRange(location: 0, length: 4), color: .red)
        
        let note2 = manager.createRichTextNote(
            title: "重要事项",
            richText: richText,
            folder: personalFolder
        )
        
        // 设置收藏
        manager.toggleNoteFavorite(note2)
    }
}
```

## 🔄 数据流程

### 创建笔记流程
1. 用户调用 `createNote()` 或 `createRichTextNote()`
2. ScriptManager 创建 Note 实例
3. 如果有富文本，通过 RichTextUtils 序列化
4. 插入到 SwiftData 上下文
5. 保存到持久化存储

### 删除恢复流程
1. 软删除：标记 `isDeleted = true`，移动到最近删除文件夹
2. 恢复：标记 `isDeleted = false`，移动到指定文件夹
3. 永久删除：从数据库中完全移除
4. 自动清理：30天后自动永久删除

### 富文本处理流程
1. 用户编辑富文本内容
2. 通过 RichTextUtils 序列化为 Data
3. 存储到 Note.richTextData
4. 同时更新 Note.content 为纯文本（兼容性）
5. 读取时反序列化为 NSAttributedString

## ⚡ 性能优化

### 大文本处理
- 超过1万字的文本使用优化的序列化方式
- 延迟加载富文本数据
- 相邻相同样式片段自动合并

### 查询优化
- 使用 SwiftData 的 Predicate 进行高效查询
- 文件夹缓存机制
- 分页加载支持

### 内存管理
- 富文本数据按需加载
- 自动释放不使用的资源
- 批量操作优化

## 🚀 扩展建议

1. **UI界面**: 基于这些数据模型创建用户界面
2. **云同步**: 利用现有数据结构实现iCloud同步
3. **导入导出**: 支持Markdown、PDF等格式
4. **搜索增强**: 全文搜索和智能推荐
5. **协作功能**: 多用户协作编辑
6. **插件系统**: 支持第三方扩展

## 📋 注意事项

1. **仅数据层**: 当前版本只包含数据模型和管理器
2. **iOS 17+**: 需要iOS 17或更高版本（SwiftData要求）
3. **富文本兼容**: 保持与纯文本的向后兼容
4. **原子操作**: 所有操作都是原子化的，确保数据一致性
5. **错误处理**: 完整的错误处理和日志记录

这个数据模型为智能提词器应用提供了坚实的基础，支持复杂的文件管理和富文本编辑需求。
