//
//  NoteView+TopBar.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/19.
//

import SwiftUI

// MARK: - 头部视图
extension NoteView {
    
    @ViewBuilder
    func topBarSection() -> some View {
        VStack(spacing: 0) {
            HStack(spacing: AppConstrants.Spacing.s) {
                Image(systemName: "increase.indent")
                    .font(.appTitle2).fontWeight(.bold)
                    .foregroundStyle(.accent)
                
                Text("Teleprompter")
                    .font(.appTitle2).fontWeight(.semibold)
                
                Spacer()
                
                // 搜索+更多
//                searchAndMore()
                
                searchButton()
            }
            .frame(maxWidth: .infinity)
            .frame(height: AppConstrants.Size.Button.heightM)
            .padding(.horizontal, AppConstrants.Spacing.l)
//            .background(Material.bar)
            
            HStack(spacing: AppConstrants.Spacing.m) {
                allPromptButton()
                folderButton()
                sortButton()
            }
            .padding(.top, AppConstrants.Spacing.m)
            .padding(.horizontal, AppConstrants.Spacing.l)
        }
    }
        
    // 搜搜按钮
    @ViewBuilder
    func searchButton() -> some View {
        Button {
            
        } label: {
            HStack(spacing: AppConstrants.Spacing.xs) {
                Image(systemName: AppIcons.Navigation.search)
                    .foregroundStyle(.appLabelPrimary.opacity(0.5))
                    .font(.appFootnote).fontWeight(.medium)
                Text("Search")
                    .font(.appFootnote).fontWeight(.medium)
                    .foregroundStyle(.appLabelPrimary.opacity(0.5))
            }
            .padding(.horizontal, AppConstrants.Spacing.s)
            .frame(height: 32)
            .background(.gray.opacity(0.15))
            .clipShape(.capsule)
        }
    }

    // 全部文件按钮
    @ViewBuilder
    func allPromptButton() -> some View {
        let isSelected = viewModel.selectedFolder?.type == .allNotes
        Button {
            /// 设置选中所有文件的文件夹夹
            viewModel.selectAllNotesFolder()
        } label: {
            Text("All Scripts")
                .font(.appFootnote).fontWeight(.medium)
                .foregroundStyle(isSelected ? .appLabelOnDark : .appLabelSecondary)
                .padding(.horizontal, AppConstrants.Spacing.m)
                .frame(height: 32)
                .background(
                    isSelected ?
                    LinearGradient(
                        colors: [.accent.opacity(0.95), .accent.opacity(0.85)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                    : LinearGradient(
                        colors: [.gray.opacity(0.3)],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
                .clipShape(RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.m))
        }
    }
    
    /// 文件夹按钮
    @ViewBuilder
    func folderButton() -> some View {
        
        Menu {
            
            if viewModel.menuFolders.isEmpty {
                // 添加文件夹
                AppMenuManager.createMenuItem("Add Folders", icon: "folder.badge.plus") {
                    viewModel.addFolderAlert()
                }
            } else {
                Picker("Folders", selection: $viewModel.selectedFolder) {
                    ForEach(viewModel.menuFolders) { folder in
                        Text(folder.name)
                            .tag(folder)
                    }
                }
                // 添加文件夹
                AppMenuManager.createMenuItem("Add Folder",
                                              icon: "folder.badge.plus") {
                    viewModel.addFolderAlert()
                }
                // 管理文件夹
                AppMenuManager.createMenuItem("Manager Folders", icon: "folder.badge.gearshape") {
                    
                }
            }
            
        } label: {
            HStack(spacing: AppConstrants.Spacing.xs) {
                Text(viewModel.menuFolderName)
                    .foregroundStyle(.appLabelPrimary.opacity(0.5))
                    .font(.appFootnote).fontWeight(.medium)
                    .lineLimit(1)

                Spacer()
                Image(systemName: "chevron.down")
                    .foregroundStyle(.appLabelSecondary.opacity(0.8))
                    .font(.appFootnote).fontWeight(.medium)
            }
            .padding(.horizontal, AppConstrants.Spacing.m)
            .frame(height: 34)
            .background(.gray.opacity(0.15))
            .clipShape(RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.m))
        }
    }
    
    /// 排序按钮
    @ViewBuilder
    func sortButton() -> some View {

        Menu {
            
            /// https://www.joshspadd.com/2024/02/swiftui-menu-button-with-subtitles/
            /// 使用这种方案 才能显示两行文本
            Button { /* Action */ } label: {
                Text("Sort By")
                Text(viewModel.noteSortMode.displayMenuName)
                Image(systemName: "arrow.up.arrow.down")
            }
            
            Picker("Sort By", selection: $viewModel.noteSortMode) {
                ForEach(NoteSortType.allCases, id: \.self) { option in
                    Label(option.displayMenuName, systemImage: option.displayIcon)
                        .tag(option)
                }
            }

            Picker("Ascending" ,selection: $viewModel.noteAscending) {
                ForEach(NoteSortAscending.allCases, id: \.self) { option in
                    Text(viewModel.noteSortMode.isTimeSort ? option.displayName : option.displayAscendName)
                        .tag(option)
                }
            }
        } label: {
            HStack(spacing: AppConstrants.Spacing.xs) {
                Text(viewModel.noteSortMode.displayName)
                    .foregroundStyle(.appLabelPrimary.opacity(0.5))
                    .font(.appFootnote).fontWeight(.medium)
                    .lineLimit(1)
                
                Spacer()
                Image(systemName: "chevron.down")
                    .foregroundStyle(.appLabelSecondary.opacity(0.8))
                    .font(.appFootnote).fontWeight(.medium)
            }
            .padding(.horizontal, AppConstrants.Spacing.m)
            .frame(height: 34)
            .background(.gray.opacity(0.15))
            .clipShape(RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.m))
        }
    }
}
