//
//  NoteView+List.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/19.
//

import SwiftUI

// MARK: - 笔记列表
extension NoteView {
    
    @ViewBuilder
    func listSection() -> some View {
        if viewModel.displayedNotes.isEmpty {
            // 显示空视图
            NoteEmptyView()
        } else {
            // 显示笔记列表
            ScrollView(.vertical) {
                LazyVStack(spacing: AppConstrants.Spacing.s) {
                    ForEach(viewModel.displayedNotes) { note in
                        NoteViewItem(note: note, viewModel: viewModel)
                            .modifier(NoteItemMenuModifier(note: note, viewModel: viewModel, containPreview: true))
                    }
                }
                .padding(.top, AppConstrants.Spacing.m)
                .padding(.bottom, AppConstrants.safeAreaBot + 68 + AppConstrants.Spacing.xxl)
            }
            .animation(.easeInOut(duration: 0.2), value: viewModel.displayedNotes.count)
        }
    }
}

// MARK: - 每个Item
struct NoteViewItem: View {
    
    var note: Note
    var viewModel: NoteViewModel
    @State private var isPressed = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: AppConstrants.Spacing.s) {
            Group {
                HStack {
                    Text(note.title)
                        .foregroundStyle(.appLabelPrimary)
                        .font(.appTitle3).fontWeight(.medium)
                    Spacer()
                    
                    Button {
                        // 点击收藏
                        note.toggleFavorite()
                        LOG_INFO("点击了收藏")
                    } label: {
                        Image(systemName: note.isFavorite ? "bookmark.fill" : "bookmark")
                            .foregroundStyle(note.isFavorite ? .orange : .appLabelSecondary.opacity(0.8))
                            .font(.system(size: 15, weight: .medium))
                    }
                }
                .padding(.top, AppConstrants.Spacing.m)

                Text(note.content)
                    .foregroundStyle(Color.appLabelSecondary)
                    .font(.appSubheadline).fontWeight(.regular)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                    .padding(.bottom, AppConstrants.Spacing.xs)
            }
            .padding(.horizontal, AppConstrants.Spacing.l)
            
            Color(.appLabelPrimary)
                .opacity(0.05)
                .frame(height: 1)
            
            HStack {
                Text(note.createdAt.description)
                    .foregroundStyle(.appLabelSecondary)
                    .font(.appFootnote).fontWeight(.medium)
                
                Spacer()
                
                Button {
                    // 更多
                    LOG_INFO("点击了更多")
                } label: {
                    HStack {
                        Spacer()
                        Image(systemName: "ellipsis")
                            .foregroundStyle(.appLabelPrimary)
                            .font(.appBody).fontWeight(.medium)
                    }
                    .frame(width: 60, height: 20)
                }
                .contentShape(Rectangle())
                .modifier(NoteItemMenuModifier(note: note, viewModel: viewModel, containPreview: false))
            }
            .frame(height: 20)
            .padding(.horizontal, AppConstrants.Spacing.l)
            .padding(.bottom, AppConstrants.Spacing.s)
        }
        .background(.appBackgroundSecondary)
        .clipShape(RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.l))
        .padding(.horizontal, AppConstrants.Spacing.s)
        .shadow(color: .black.opacity(0.02), radius: 10, x: 0, y: 0)
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(AppConstrants.Animation.microSpring, value: isPressed)
        .onTapGesture {
            withAnimation(AppConstrants.Animation.ultraFast) {
                isPressed = true
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(AppConstrants.Animation.ultraFast) {
                    isPressed = false
                }
            }

            // 这里可以添加导航到详情页的逻辑
            LOG_INFO("点击了文本")
        }
    }
}

struct NotePreviewViewItem: View {
    var note: Note
    
    var body: some View {
        VStack(alignment: .leading, spacing: AppConstrants.Spacing.s) {
            Text(note.title)
                .foregroundStyle(.appLabelPrimary)
                .font(.appTitle3).fontWeight(.medium)

            Text(note.content)
                .foregroundStyle(Color.appLabelSecondary)
                .font(.appSubheadline).fontWeight(.regular)
                .lineLimit(20)
                .multilineTextAlignment(.leading)
                .padding(.bottom, AppConstrants.Spacing.xs)
        }
        .padding(.horizontal, AppConstrants.Spacing.l)
        .padding(.vertical, AppConstrants.Spacing.m)
        .frame(width: 340, height: 240) // 同时设置宽度和高度
        .background(.appBackgroundSecondary)
        .clipShape(RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.l))
        .fixedSize() // 强制使用指定尺寸
    }
}

// MARK: - 长按的modifier
struct NoteItemMenuModifier: ViewModifier {
    
    var note: Note
    var viewModel: NoteViewModel
    var containPreview: Bool
    
    func body(content: Content) -> some View {
        let previewGroup = Group {
            AppMenuManager.createMenuItem("Edit", icon: AppIcons.Action.edit) {
                viewModel.selectedNote = note
                viewModel.showEditView = true
            }
            AppMenuManager.createSubMenu("Move to folder", icon: "folder") {
                ForEach(viewModel.canMoveToFolders) { folder in
                    let icon = folder == viewModel.selectedFolder ? "checkmark" : nil
                    AppMenuManager.createMenuItem(folder.name,
                                                  icon: icon) {
                        // 移动note到选中的文件夹
                        viewModel.moveNote(note, to: folder)
                    }
                }
            }
            if note.isFavorite {
                AppMenuManager.createMenuItem("Remove Bookmark", icon: "bookmark.slash") {
                    note.toggleFavorite()
                }
            } else {
                AppMenuManager.createMenuItem("Add Bookmark", icon: "bookmark") {
                    note.toggleFavorite()
                }
            }
            AppMenuManager.createDivider()
            AppMenuManager.createMenuItem("Delete", icon: nil, isDestructive: true) {
                AppMenuManager.showActionSheetConfirmation(title: "Delete script",
                                                           message: "Are you sure you want to delete this script? This action cannot be undone.",
                                                           confirmTitle: "Delete",
                                                           cancelTitle: "Go back",
                                                           isDestructive: true) {
                    viewModel.deleteNote(note)
                }
            }
        }
        if containPreview {
            content
                .addContextMenuWithPreview {
                    previewGroup
                } preview: {
                    NotePreviewViewItem(note: note)
                }
        } else {
            content
                .addDropdownMenu {
                    previewGroup
                }
        }
        
    }
}
