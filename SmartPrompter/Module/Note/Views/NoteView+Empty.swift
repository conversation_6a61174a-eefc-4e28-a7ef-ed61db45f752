//
//  NoteView+Empty.swift
//  SmartPrompter
//
//  Created by 郭炜 on 2025/7/22.
//

import SwiftUI

// MARK: - 空视图
struct NoteEmptyView: View {

    var body: some View {
        ZStack {
            // 点状背景图案
            DotPatternBackground()

            VStack(spacing: AppConstrants.Spacing.xl) {
                Spacer()

                // 主要内容
                VStack(spacing: AppConstrants.Spacing.l) {
                    // 标题文本
                    Text("Create your first script.")
                        .font(.appTitle3)
                        .fontWeight(.medium)
                        .foregroundStyle(.appLabelSecondary)
                    
                    Text("Tap the plus button to get started.")
                        .font(.appLabel)
                        .fontWeight(.medium)
                        .foregroundStyle(.appLabelSecondary)
                }

                Spacer()
            }
        }
        .frame(maxWidth: 240, maxHeight: 240)
    }
}

// MARK: - 点状背景图案
struct DotPatternBackground: View {
    private let dotSize: CGFloat = 4
    private let spacing: CGFloat = 20
    private let fadeDistance: CGFloat = 120 // 渐隐区域的距离

    var body: some View {
        GeometryReader { geometry in
            let columns = Int(geometry.size.width / spacing) + 1
            let rows = Int(geometry.size.height / spacing) + 1

            VStack(spacing: spacing) {
                ForEach(0..<rows, id: \.self) { row in
                    HStack(spacing: spacing) {
                        ForEach(0..<columns, id: \.self) { column in
                            let dotOpacity = calculateDotOpacity(
                                row: row,
                                column: column,
                                totalRows: rows,
                                totalColumns: columns,
                                geometry: geometry
                            )

                            Circle()
                                .fill(.appLabelSecondary.opacity(dotOpacity))
                                .frame(width: dotSize, height: dotSize)
                        }
                    }
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
        }
    }

    /// 计算点的透明度，基于距离边缘的位置实现四周渐隐
    private func calculateDotOpacity(
        row: Int,
        column: Int,
        totalRows: Int,
        totalColumns: Int,
        geometry: GeometryProxy
    ) -> Double {
        // 计算点的实际位置
        let x = CGFloat(column) * spacing
        let y = CGFloat(row) * spacing

        // 计算到各边缘的距离
        let distanceToLeft = x
        let distanceToRight = geometry.size.width - x
        let distanceToTop = y
        let distanceToBottom = geometry.size.height - y

        // 找到最小距离（最接近的边缘）
        let minDistanceToEdge = min(distanceToLeft, distanceToRight, distanceToTop, distanceToBottom)

        // 基础透明度
        let baseOpacity: Double = 0.35

        // 如果距离边缘小于渐隐距离，则计算渐隐透明度
        if minDistanceToEdge < fadeDistance {
            let fadeRatio = minDistanceToEdge / fadeDistance
            return baseOpacity * fadeRatio
        } else {
            return baseOpacity
        }
    }
}

#Preview {
    NoteEmptyView()
}
