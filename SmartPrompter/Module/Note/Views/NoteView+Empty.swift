//
//  NoteView+Empty.swift
//  SmartPrompter
//
//  Created by 郭炜 on 2025/7/22.
//

import SwiftUI

// MARK: - 空视图
struct NoteEmptyView: View {

    var body: some View {
        ZStack {
            // 点状背景图案
            DotPatternBackground()

            VStack(spacing: AppConstrants.Spacing.xl) {
                Spacer()

                // 主要内容
                VStack(spacing: AppConstrants.Spacing.l) {
                    // 标题文本
                    Text("Create your first script.")
                        .font(.appTitle3)
                        .fontWeight(.medium)
                        .foregroundStyle(.appLabelSecondary)
                    
                    Text("Tap the plus button to get started.")
                        .font(.appLabel)
                        .fontWeight(.medium)
                        .foregroundStyle(.appLabelSecondary)
                }

                Spacer()
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(.appBackgroundPrimary)
    }
}

// MARK: - 点状背景图案
struct DotPatternBackground: View {
    private let dotSize: CGFloat = 4
    private let spacing: CGFloat = 20

    var body: some View {
        GeometryReader { geometry in
            let columns = Int(geometry.size.width / spacing) + 1
            let rows = Int(geometry.size.height / spacing) + 1

            VStack(spacing: spacing) {
                ForEach(0..<rows, id: \.self) { row in
                    HStack(spacing: spacing) {
                        ForEach(0..<columns, id: \.self) { column in
                            Circle()
                                .fill(.appLabelSecondary.opacity(0.15))
                                .frame(width: dotSize, height: dotSize)
                        }
                    }
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
        }
    }
}

#Preview {
    NoteEmptyView()
}
