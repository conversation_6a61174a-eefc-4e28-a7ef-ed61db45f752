//
//  NoteView.swift
//  SmartTeleprompter
//
//  Created by ryan on 2025/7/19.
//

import SwiftUI
import SwiftData

// MARK: - 笔记视图
struct NoteView: View {

    @Environment(ScriptManager.self) var scriptManager
    @State var viewModel = NoteViewModel()
    
    var body: some View {
        VStack {
            // 顶部导航栏
            topBarSection()
            
            Spacer()
                .frame(height: AppConstrants.Spacing.m)

            ZStack {
                // 列表视图
                listSection()

                VStack(spacing: 0) {
                    Spacer()
                    // 底部控制栏
                    bottomBarSection()
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(.appBackgroundPrimary)
        .onAppear {
            // 设置 ScriptManager 并加载数据
            viewModel.setScriptManager(scriptManager)
        }
        .fullScreenCover(isPresented: $viewModel.showEditView) {
            EditNoteView(viewModel: viewModel, note: viewModel.selectedNote)
        }
    }
}

#Preview {
    let schema = Schema([
        Folder.self,
        Note.self
    ])
    let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: schema, configurations: [modelConfiguration])
    let scriptManager = ScriptManager(modelContext: container.mainContext)
    
    return NoteView()
        .environment(scriptManager)
}
