//
//  NoteView+BottomBar.swift
//  SmartPrompter
//
//  Created by ryan on 2025/7/19.
//

import SwiftUI

// MARK: - 底部的选择视图
extension NoteView {
    
    @ViewBuilder
    func bottomBarSection() -> some View {
        HStack(alignment: .center, spacing: 0) {
            noteButton()
            plusButton()
            profileButton()
        }
        .padding(.horizontal, AppConstrants.Spacing.s)
        .frame(width: 240, height: 62)
        .background(
            LinearGradient(
                colors: [.accent, .accent.opacity(0.9)],
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .clipShape(.capsule)
        .padding(.bottom, AppConstrants.Spacing.xxs)
        .shadow(AppConstrants.Shadow.extraHeavy)
        .overlay(
            Capsule()
                .stroke(.white.opacity(0.2), lineWidth: 1)
                .blendMode(.overlay)
        )
    }
    
    @ViewBuilder
    func noteButton() -> some View {
        Button {
            
        } label: {
            Image(systemName: "tray.fill")
                .foregroundStyle(.appLabelOnDark)
                .font(.system(size: AppConstrants.Size.Icon.m))
        }
        .frame(maxWidth: .infinity)
    }
    
    @ViewBuilder
    func plusButton() -> some View {
        Button {
            actionForPlusNote()
        } label: {
            Image(systemName: "plus")
                .foregroundStyle(.appLabelOnDark)
                .font(.system(size: AppConstrants.Size.Icon.s, weight: .bold))
                .frame(width: 68, height: 40)
                .background(.appBackgroundPrimary.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: AppConstrants.CornerRadius.l))
        }
        .frame(maxWidth: .infinity)
        .primaryButtonStyle()
    }
    
    @ViewBuilder
    func profileButton() -> some View {
        Button {
            
        } label: {
            Image(systemName: "person.crop.circle")
                .foregroundStyle(.appLabelSecondary)
                .font(.system(size: AppConstrants.Size.Icon.m))
        }
        .frame(maxWidth: .infinity)
    }
}
