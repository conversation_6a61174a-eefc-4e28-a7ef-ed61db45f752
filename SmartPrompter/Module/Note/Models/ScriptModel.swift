//
//  ScriptModel.swift
//  SmartTeleprompter
//
//  Created by ryan on 2025/7/18.
//

import Foundation
import SwiftData
import UIKit

// MARK: - 文件夹类型枚举

/// 文件夹类型，用于区分默认文件夹和自定义文件夹
enum FolderType: String, Codable, CaseIterable {
    case allNotes = "all_notes"           // 所有笔记（默认）
    case recentlyDeleted = "recently_deleted"  // 最近删除（默认）
    case custom = "custom"                // 自定义文件夹
    
    /// 是否为默认文件夹（不可删除、重命名）
    var isDefault: Bool {
        return self != .custom
    }
    
    /// 默认显示名称
    var defaultName: String {
        switch self {
        case .allNotes:
            return "All Notes"
        case .recentlyDeleted:
            return "最近删除"
        case .custom:
            return "新文件夹"
        }
    }
    
    /// 默认图标
    var defaultIcon: String {
        switch self {
        case .allNotes:
            return "doc.text"
        case .recentlyDeleted:
            return "trash"
        case .custom:
            return "folder"
        }
    }
}

// MARK: - 文件夹数据模型

/// 文件夹数据模型
/// 支持默认文件夹（All Notes、最近删除）和自定义文件夹
@Model
final class Folder: Equatable {
    
    // MARK: - 基础属性
    
    /// 唯一标识符
    var id: UUID
    
    /// 文件夹名称
    var name: String
    
    /// 文件夹图标（SF Symbol名称）
    var icon: String
    
    /// 文件夹类型
    var type: FolderType
    
    /// 创建时间
    var createdAt: Date
    
    /// 修改时间
    var modifiedAt: Date
    
    /// 排序权重（用于自定义排序）
    var sortOrder: Int
    
    // MARK: - 关系属性
    
    /// 文件夹中的笔记列表
    @Relationship(deleteRule: .cascade, inverse: \Note.folder)
    var notes: [Note]
    
    // MARK: - 初始化方法
    
    /// 初始化文件夹
    /// - Parameters:
    ///   - name: 文件夹名称，如果为nil则使用类型默认名称
    ///   - icon: 文件夹图标，如果为nil则使用类型默认图标
    ///   - type: 文件夹类型，默认为自定义类型
    ///   - sortOrder: 排序权重，默认为0
    init(
        name: String? = nil,
        icon: String? = nil,
        type: FolderType = .custom,
        sortOrder: Int = 0
    ) {
        self.id = UUID()
        self.name = name ?? type.defaultName
        self.icon = icon ?? type.defaultIcon
        self.type = type
        self.createdAt = Date()
        self.modifiedAt = Date()
        self.sortOrder = sortOrder
        self.notes = []
    }
    
    // MARK: - 计算属性
    
    /// 是否为默认文件夹
    var isDefault: Bool {
        return type.isDefault
    }
    
    /// 笔记数量（不包括已删除的笔记）
    var noteCount: Int {
        return notes.filter { !$0.isDeleted }.count
    }
    
    /// 已删除的笔记数量
    var deletedNoteCount: Int {
        return notes.filter { $0.isDeleted }.count
    }
    
    /// 收藏的笔记数量
    var favoriteNoteCount: Int {
        return notes.filter { $0.isFavorite && !$0.isDeleted }.count
    }
    
    // MARK: - 实例方法
    
    /// 更新修改时间
    func updateModifiedTime() {
        self.modifiedAt = Date()
    }
    
    /// 更新文件夹信息（仅限自定义文件夹）
    /// - Parameters:
    ///   - name: 新名称
    ///   - icon: 新图标
    /// - Returns: 是否更新成功
    @discardableResult
    func updateInfo(name: String? = nil, icon: String? = nil) -> Bool {
        // 默认文件夹不允许修改
        guard !isDefault else { return false }
        
        if let newName = name, !newName.isEmpty {
            self.name = newName
        }
        
        if let newIcon = icon, !newIcon.isEmpty {
            self.icon = newIcon
        }
        
        updateModifiedTime()
        return true
    }
    
    /// 添加笔记到文件夹
    /// - Parameter note: 要添加的笔记
    func addNote(_ note: Note) {
        note.folder = self
        updateModifiedTime()
    }
    
    /// 从文件夹中移除笔记
    /// - Parameter note: 要移除的笔记
    func removeNote(_ note: Note) {
        note.folder = nil
        updateModifiedTime()
    }
    
    /// 获取有效笔记（未删除的笔记）
    /// - Returns: 未删除的笔记数组
    func getActiveNotes() -> [Note] {
        return notes.filter { !$0.isDeleted }
    }
    
    /// 获取已删除的笔记
    /// - Returns: 已删除的笔记数组
    func getDeletedNotes() -> [Note] {
        return notes.filter { $0.isDeleted }
    }
    
    /// 获取收藏的笔记
    /// - Returns: 收藏的笔记数组
    func getFavoriteNotes() -> [Note] {
        return notes.filter { $0.isFavorite && !$0.isDeleted }
    }
}

// MARK: - Folder扩展

extension Folder {
    
    /// 创建默认的"All Notes"文件夹
    /// - Returns: All Notes文件夹实例
    static func createAllNotesFolder() -> Folder {
        return Folder(type: .allNotes, sortOrder: -1000)
    }
    
    /// 创建默认的"最近删除"文件夹
    /// - Returns: 最近删除文件夹实例
    static func createRecentlyDeletedFolder() -> Folder {
        return Folder(type: .recentlyDeleted, sortOrder: 1000)
    }
    
    /// 创建自定义文件夹
    /// - Parameters:
    ///   - name: 文件夹名称
    ///   - icon: 文件夹图标，可选
    ///   - sortOrder: 排序权重，可选
    /// - Returns: 自定义文件夹实例
    static func createCustomFolder(
        name: String,
        icon: String? = nil,
        sortOrder: Int = 0
    ) -> Folder {
        return Folder(
            name: name,
            icon: icon,
            type: .custom,
            sortOrder: sortOrder
        )
    }
}

// MARK: - Folder协议实现

extension Folder: Identifiable, Hashable {
    
    /// 哈希值计算
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    /// 相等性比较
    static func == (lhs: Folder, rhs: Folder) -> Bool {
        return lhs.id == rhs.id
    }
}

// MARK: - Folder调试信息

extension Folder: CustomStringConvertible {

    /// 调试描述
    var description: String {
        return "Folder(id: \(id), name: \"\(name)\", type: \(type), noteCount: \(noteCount))"
    }
}

// MARK: - 笔记数据模型

/// 笔记数据模型
/// 支持富文本内容、收藏、软删除等功能
@Model
final class Note {

    // MARK: - 基础属性

    /// 唯一标识符
    var id: UUID

    /// 笔记标题
    var title: String

    /// 笔记正文（纯文本，用于搜索和向后兼容）
    var content: String

    /// 富文本内容（序列化的NSAttributedString）
    var richTextData: Data?

    /// 创建时间
    var createdAt: Date

    /// 最后编辑时间
    var modifiedAt: Date

    /// 是否收藏
    var isFavorite: Bool

    /// 是否已删除（软删除标记）
    var isDeleted: Bool

    /// 删除时间（用于最近删除功能）
    var deletedAt: Date?

    /// 排序权重（用于自定义排序）
    var sortOrder: Int

    // MARK: - 关系属性

    /// 所属文件夹
    var folder: Folder?

    // MARK: - 初始化方法

    /// 初始化笔记
    /// - Parameters:
    ///   - title: 笔记标题
    ///   - content: 笔记内容（纯文本）
    ///   - richText: 富文本内容，可选
    ///   - folder: 所属文件夹，可选
    ///   - isFavorite: 是否收藏，默认为false
    ///   - sortOrder: 排序权重，默认为0
    init(
        title: String,
        content: String = "",
        richText: NSAttributedString? = nil,
        folder: Folder? = nil,
        isFavorite: Bool = false,
        sortOrder: Int = 0
    ) {
        self.id = UUID()
        self.title = title
        self.content = content
        self.createdAt = Date()
        self.modifiedAt = Date()
        self.isFavorite = isFavorite
        self.isDeleted = false
        self.deletedAt = nil
        self.sortOrder = sortOrder
        self.folder = folder
        self.richTextData = nil

        // 如果提供了富文本，则序列化存储
        if let richText = richText {
            self.richTextData = RichTextUtils.serialize(richText)
        }
    }

    // MARK: - 计算属性

    /// 获取有效的文本内容（优先使用富文本的纯文本）
    var effectiveContent: String {
        if let richText = getRichText() {
            return RichTextUtils.getPlainText(from: richText)
        }
        return content
    }

    /// 获取字符数
    var characterCount: Int {
        return effectiveContent.count
    }

    /// 获取字数
    var wordCount: Int {
        return RichTextUtils.getWordCount(from: effectiveContent)
    }

    /// 是否包含富文本格式
    var hasRichTextFormatting: Bool {
        guard let richText = getRichText() else { return false }
        return RichTextUtils.hasFormatting(richText)
    }

    /// 是否为空笔记
    var isEmpty: Bool {
        return title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               effectiveContent.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    // MARK: - 富文本相关方法

    /// 设置富文本内容
    /// - Parameter richText: 富文本内容
    func setRichText(_ richText: NSAttributedString) {
        // 序列化富文本
        self.richTextData = RichTextUtils.serialize(richText)
        // 同时更新纯文本内容以保持兼容性
        self.content = RichTextUtils.getPlainText(from: richText)
        updateModifiedTime()
    }

    /// 获取富文本内容
    /// - Returns: 富文本内容，如果没有则返回nil
    func getRichText() -> NSAttributedString? {
        guard let data = richTextData else { return nil }
        return RichTextUtils.deserialize(data)
    }

    /// 获取富文本内容，如果没有则从纯文本创建
    /// - Returns: 富文本内容
    func getRichTextOrCreate() -> NSAttributedString {
        if let richText = getRichText() {
            return richText
        }
        // 从纯文本创建富文本
        return RichTextUtils.createAttributedString(from: content,
                                                    textColor: UIColor.label)
    }

    /// 更新纯文本内容
    /// - Parameter newContent: 新的纯文本内容
    func updateContent(_ newContent: String) {
        self.content = newContent
        // 清除富文本数据，因为纯文本已更改
        self.richTextData = nil
        updateModifiedTime()
    }

    /// 清除富文本格式，只保留纯文本
    func clearRichTextFormatting() {
        self.richTextData = nil
        updateModifiedTime()
    }

    // MARK: - 基础操作方法

    /// 更新修改时间
    func updateModifiedTime() {
        self.modifiedAt = Date()
    }

    /// 更新笔记基本信息
    /// - Parameters:
    ///   - title: 新标题，可选
    ///   - content: 新内容，可选
    func updateInfo(title: String? = nil, content: String? = nil) {
        if let newTitle = title {
            self.title = newTitle
        }

        if let newContent = content {
            updateContent(newContent)
        } else {
            updateModifiedTime()
        }
    }

    /// 切换收藏状态
    func toggleFavorite() {
        self.isFavorite.toggle()
        updateModifiedTime()
    }

    /// 设置收藏状态
    /// - Parameter favorite: 是否收藏
    func setFavorite(_ favorite: Bool) {
        self.isFavorite = favorite
        updateModifiedTime()
    }

    // MARK: - 删除相关方法

    /// 软删除笔记（移动到最近删除）
    func softDelete() {
        self.isDeleted = true
        self.deletedAt = Date()
        updateModifiedTime()
    }

    /// 恢复已删除的笔记
    func restore() {
        self.isDeleted = false
        self.deletedAt = nil
        updateModifiedTime()
    }

    /// 检查是否可以永久删除（删除超过30天）
    /// - Returns: 是否可以永久删除
    func canPermanentlyDelete() -> Bool {
        guard let deletedAt = deletedAt else { return false }
        let thirtyDaysAgo = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
        return deletedAt < thirtyDaysAgo
    }

    // MARK: - 移动相关方法

    /// 移动到指定文件夹
    /// - Parameter targetFolder: 目标文件夹
    func moveTo(folder targetFolder: Folder?) {
        // 从当前文件夹移除
        self.folder?.removeNote(self)

        // 添加到目标文件夹
        self.folder = targetFolder
        targetFolder?.addNote(self)

        updateModifiedTime()
    }
}

// MARK: - Note扩展

extension Note {

    /// 创建新笔记
    /// - Parameters:
    ///   - title: 笔记标题
    ///   - content: 笔记内容
    ///   - folder: 所属文件夹，如果为nil则不分配文件夹
    /// - Returns: 新创建的笔记
    static func createNote(
        title: String,
        content: String = "",
        folder: Folder? = nil
    ) -> Note {
        return Note(
            title: title,
            content: content,
            folder: folder
        )
    }

    /// 创建富文本笔记
    /// - Parameters:
    ///   - title: 笔记标题
    ///   - richText: 富文本内容
    ///   - folder: 所属文件夹，如果为nil则不分配文件夹
    /// - Returns: 新创建的笔记
    static func createRichTextNote(
        title: String,
        richText: NSAttributedString,
        folder: Folder? = nil
    ) -> Note {
        return Note(
            title: title,
            richText: richText,
            folder: folder
        )
    }

    /// 从纯文本快速创建笔记
    /// - Parameters:
    ///   - text: 文本内容，第一行作为标题，其余作为正文
    ///   - folder: 所属文件夹
    /// - Returns: 新创建的笔记
    static func createFromText(
        _ text: String,
        folder: Folder? = nil
    ) -> Note {
        let lines = text.components(separatedBy: .newlines)
        let title = lines.first?.trimmingCharacters(in: .whitespacesAndNewlines) ?? "无标题"
        let content = lines.dropFirst().joined(separator: "\n").trimmingCharacters(in: .whitespacesAndNewlines)

        return Note(
            title: title,
            content: content,
            folder: folder
        )
    }
}

// MARK: - Note协议实现

extension Note: Identifiable, Hashable {

    /// 哈希值计算
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    /// 相等性比较
    static func == (lhs: Note, rhs: Note) -> Bool {
        return lhs.id == rhs.id
    }
}

// MARK: - Note调试信息

extension Note: CustomStringConvertible {

    /// 调试描述
    var description: String {
        let folderName = folder?.name ?? "无文件夹"
        let status = isDeleted ? "已删除" : (isFavorite ? "收藏" : "普通")
        return "Note(id: \(id), title: \"\(title)\", folder: \"\(folderName)\", status: \(status), wordCount: \(wordCount))"
    }
}

// MARK: - 搜索和排序支持

extension Note {

    /// 检查笔记是否匹配搜索关键词
    /// - Parameter query: 搜索关键词
    /// - Returns: 是否匹配
    func matches(query: String) -> Bool {
        let lowercaseQuery = query.lowercased()
        return title.lowercased().contains(lowercaseQuery) ||
               effectiveContent.lowercased().contains(lowercaseQuery)
    }

    /// 获取搜索匹配的相关性分数
    /// - Parameter query: 搜索关键词
    /// - Returns: 相关性分数（0-100）
    func relevanceScore(for query: String) -> Int {
        let lowercaseQuery = query.lowercased()
        let lowercaseTitle = title.lowercased()
        let lowercaseContent = effectiveContent.lowercased()

        var score = 0

        // 标题完全匹配得分最高
        if lowercaseTitle == lowercaseQuery {
            score += 100
        } else if lowercaseTitle.contains(lowercaseQuery) {
            score += 50
        }

        // 内容匹配得分较低
        if lowercaseContent.contains(lowercaseQuery) {
            score += 20
        }

        // 收藏的笔记额外加分
        if isFavorite {
            score += 10
        }

        // 最近修改的笔记额外加分
        let daysSinceModified = Calendar.current.dateComponents([.day], from: modifiedAt, to: Date()).day ?? 0
        if daysSinceModified < 7 {
            score += 5
        }

        return min(score, 100)
    }
}

// MARK: - 排序比较器

extension Note {

    /// 按修改时间排序（最新的在前）
    static func sortByModifiedDate(_ lhs: Note, _ rhs: Note) -> Bool {
        return lhs.modifiedAt > rhs.modifiedAt
    }

    /// 按创建时间排序（最新的在前）
    static func sortByCreatedDate(_ lhs: Note, _ rhs: Note) -> Bool {
        return lhs.createdAt > rhs.createdAt
    }

    /// 按标题排序（字母顺序）
    static func sortByTitle(_ lhs: Note, _ rhs: Note) -> Bool {
        return lhs.title.localizedCaseInsensitiveCompare(rhs.title) == .orderedAscending
    }

    /// 按字数排序（字数多的在前）
    static func sortByWordCount(_ lhs: Note, _ rhs: Note) -> Bool {
        return lhs.wordCount > rhs.wordCount
    }

    /// 按收藏状态排序（收藏的在前）
    static func sortByFavorite(_ lhs: Note, _ rhs: Note) -> Bool {
        if lhs.isFavorite != rhs.isFavorite {
            return lhs.isFavorite
        }
        // 如果收藏状态相同，按修改时间排序
        return sortByModifiedDate(lhs, rhs)
    }
}
