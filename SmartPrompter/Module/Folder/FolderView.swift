//
//  FolderView.swift
//  SmartPrompter
//
//  Created by 郭炜 on 2025/7/22.
//

import SwiftUI

// MARK: - 文件夹管理视图
struct FolderView: View {
    
    @State var viewModel: NoteViewModel
    @Environment(\.dismiss) var dismiss
    
    var body: some View {
        Text(/*@START_MENU_TOKEN@*/"Hello, World!"/*@END_MENU_TOKEN@*/)
    }
}

#Preview {
    FolderView(viewModel: .init(scriptManager: nil))
}
