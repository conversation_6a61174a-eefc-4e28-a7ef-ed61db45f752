//
//  MoveFolderView.swift
//  SmartPrompter
//
//  Created by 郭炜 on 2025/7/22.
//

import SwiftUI

// MARK: - 台本移动页面
struct MoveFolderView: View {
    
    @State var viewModel: NoteViewModel
    @Environment(\.dismiss) var dismiss

    var body: some View {
        Text(/*@START_MENU_TOKEN@*/"Hello, World!"/*@END_MENU_TOKEN@*/)
    }
}

#Preview {
    MoveFolderView(viewModel: .init(scriptManager: nil))
}
