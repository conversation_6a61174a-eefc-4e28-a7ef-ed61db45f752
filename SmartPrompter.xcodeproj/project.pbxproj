// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		944010D12E2CACC600236C1E /* AppMenuManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 944010D02E2CACC600236C1E /* AppMenuManager.swift */; };
		944010D32E2CB1A600236C1E /* NoteViewModel+List.swift in Sources */ = {isa = PBXBuildFile; fileRef = 944010D22E2CB1A600236C1E /* NoteViewModel+List.swift */; };
		944010D52E2CB1D800236C1E /* NoteViewModel+Note.swift in Sources */ = {isa = PBXBuildFile; fileRef = 944010D42E2CB1D800236C1E /* NoteViewModel+Note.swift */; };
		9451474C2E2B3797008FA284 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 945147472E2B3797008FA284 /* Assets.xcassets */; };
		9451474F2E2B3797008FA284 /* SmartPrompterApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9451474A2E2B3797008FA284 /* SmartPrompterApp.swift */; };
		945147602E2B37C3008FA284 /* README.md in Resources */ = {isa = PBXBuildFile; fileRef = 9451475E2E2B37C3008FA284 /* README.md */; };
		945147612E2B37C3008FA284 /* ScriptManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 945147542E2B37C3008FA284 /* ScriptManager.swift */; };
		945147622E2B37C3008FA284 /* FolderManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 945147522E2B37C3008FA284 /* FolderManager.swift */; };
		945147632E2B37C3008FA284 /* NoteView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9451475C2E2B37C3008FA284 /* NoteView.swift */; };
		945147642E2B37C3008FA284 /* RichTextUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 945147592E2B37C3008FA284 /* RichTextUtils.swift */; };
		945147652E2B37C3008FA284 /* NoteManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 945147532E2B37C3008FA284 /* NoteManager.swift */; };
		945147662E2B37C3008FA284 /* RecentlyDeletedManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 945147582E2B37C3008FA284 /* RecentlyDeletedManager.swift */; };
		945147672E2B37C3008FA284 /* ScriptModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 945147562E2B37C3008FA284 /* ScriptModel.swift */; };
		9451476A2E2B4292008FA284 /* AppConstrants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 945147692E2B4292008FA284 /* AppConstrants.swift */; };
		9451476C2E2B479D008FA284 /* NoteViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9451476B2E2B479D008FA284 /* NoteViewModel.swift */; };
		9451476E2E2B480C008FA284 /* NoteView+BottomBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9451476D2E2B480C008FA284 /* NoteView+BottomBar.swift */; };
		945147702E2B481D008FA284 /* NoteView+TopBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9451476F2E2B481D008FA284 /* NoteView+TopBar.swift */; };
		945147722E2B5C3D008FA284 /* NoteView+List.swift in Sources */ = {isa = PBXBuildFile; fileRef = 945147712E2B5C3D008FA284 /* NoteView+List.swift */; };
		945147742E2B7F20008FA284 /* NoteView+Action.swift in Sources */ = {isa = PBXBuildFile; fileRef = 945147732E2B7F20008FA284 /* NoteView+Action.swift */; };
		945147762E2B7F67008FA284 /* AppHaptic.swift in Sources */ = {isa = PBXBuildFile; fileRef = 945147752E2B7F67008FA284 /* AppHaptic.swift */; };
		945147782E2B8082008FA284 /* AppLog.swift in Sources */ = {isa = PBXBuildFile; fileRef = 945147772E2B8082008FA284 /* AppLog.swift */; };
		9451477B2E2B80D4008FA284 /* EditNoteView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9451477A2E2B80D4008FA284 /* EditNoteView.swift */; };
		9451477D2E2B8518008FA284 /* EditNoteView+Action.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9451477C2E2B8518008FA284 /* EditNoteView+Action.swift */; };
		9451477F2E2B8575008FA284 /* EditNoteView+TopBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9451477E2E2B8575008FA284 /* EditNoteView+TopBar.swift */; };
		945147812E2B9263008FA284 /* EditNoteView+TextView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 945147802E2B9263008FA284 /* EditNoteView+TextView.swift */; };
		945147842E2B9C6A008FA284 /* RichTextEditor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 945147832E2B9C6A008FA284 /* RichTextEditor.swift */; };
		945147862E2B9D01008FA284 /* RichTextToolbar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 945147852E2B9D01008FA284 /* RichTextToolbar.swift */; };
		945147872E2BD43E008FA284 /* AppButtonStyles.swift in Sources */ = {isa = PBXBuildFile; fileRef = 945147852E2BD43E008FA284 /* AppButtonStyles.swift */; };
		945147882E2B9D02008FA284 /* RichTextEditorActions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 945147872E2B9D02008FA284 /* RichTextEditorActions.swift */; };
		945147882E2BD43E008FA284 /* AppIcons.swift in Sources */ = {isa = PBXBuildFile; fileRef = 945147862E2BD43E008FA284 /* AppIcons.swift */; };
		AF9826452E2F23470040BA71 /* FolderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF9826442E2F23470040BA71 /* FolderView.swift */; };
		AF9826492E2F24540040BA71 /* MoveFolderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF9826482E2F24540040BA71 /* MoveFolderView.swift */; };
		AF98264B2E2F262D0040BA71 /* NoteViewModel+Folder.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF98264A2E2F262D0040BA71 /* NoteViewModel+Folder.swift */; };
		AF98264D2E2F2FA80040BA71 /* NoteView+Empty.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF98264C2E2F2FA80040BA71 /* NoteView+Empty.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		944010D02E2CACC600236C1E /* AppMenuManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppMenuManager.swift; sourceTree = "<group>"; };
		944010D22E2CB1A600236C1E /* NoteViewModel+List.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "NoteViewModel+List.swift"; sourceTree = "<group>"; };
		944010D42E2CB1D800236C1E /* NoteViewModel+Note.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "NoteViewModel+Note.swift"; sourceTree = "<group>"; };
		945147372E2B378F008FA284 /* SmartPrompter.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SmartPrompter.app; sourceTree = BUILT_PRODUCTS_DIR; };
		945147472E2B3797008FA284 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		9451474A2E2B3797008FA284 /* SmartPrompterApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmartPrompterApp.swift; sourceTree = "<group>"; };
		945147522E2B37C3008FA284 /* FolderManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FolderManager.swift; sourceTree = "<group>"; };
		945147532E2B37C3008FA284 /* NoteManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NoteManager.swift; sourceTree = "<group>"; };
		945147542E2B37C3008FA284 /* ScriptManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScriptManager.swift; sourceTree = "<group>"; };
		945147562E2B37C3008FA284 /* ScriptModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScriptModel.swift; sourceTree = "<group>"; };
		945147582E2B37C3008FA284 /* RecentlyDeletedManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecentlyDeletedManager.swift; sourceTree = "<group>"; };
		945147592E2B37C3008FA284 /* RichTextUtils.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RichTextUtils.swift; sourceTree = "<group>"; };
		9451475C2E2B37C3008FA284 /* NoteView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NoteView.swift; sourceTree = "<group>"; };
		9451475E2E2B37C3008FA284 /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		945147692E2B4292008FA284 /* AppConstrants.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppConstrants.swift; sourceTree = "<group>"; };
		9451476B2E2B479D008FA284 /* NoteViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NoteViewModel.swift; sourceTree = "<group>"; };
		9451476D2E2B480C008FA284 /* NoteView+BottomBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "NoteView+BottomBar.swift"; sourceTree = "<group>"; };
		9451476F2E2B481D008FA284 /* NoteView+TopBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "NoteView+TopBar.swift"; sourceTree = "<group>"; };
		945147712E2B5C3D008FA284 /* NoteView+List.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "NoteView+List.swift"; sourceTree = "<group>"; };
		945147732E2B7F20008FA284 /* NoteView+Action.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "NoteView+Action.swift"; sourceTree = "<group>"; };
		945147752E2B7F67008FA284 /* AppHaptic.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppHaptic.swift; sourceTree = "<group>"; };
		945147772E2B8082008FA284 /* AppLog.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppLog.swift; sourceTree = "<group>"; };
		9451477A2E2B80D4008FA284 /* EditNoteView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditNoteView.swift; sourceTree = "<group>"; };
		9451477C2E2B8518008FA284 /* EditNoteView+Action.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "EditNoteView+Action.swift"; sourceTree = "<group>"; };
		9451477E2E2B8575008FA284 /* EditNoteView+TopBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "EditNoteView+TopBar.swift"; sourceTree = "<group>"; };
		945147802E2B9263008FA284 /* EditNoteView+TextView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "EditNoteView+TextView.swift"; sourceTree = "<group>"; };
		945147832E2B9C6A008FA284 /* RichTextEditor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RichTextEditor.swift; sourceTree = "<group>"; };
		945147852E2B9D01008FA284 /* RichTextToolbar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RichTextToolbar.swift; sourceTree = "<group>"; };
		945147852E2BD43E008FA284 /* AppButtonStyles.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppButtonStyles.swift; sourceTree = "<group>"; };
		945147862E2BD43E008FA284 /* AppIcons.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppIcons.swift; sourceTree = "<group>"; };
		945147872E2B9D02008FA284 /* RichTextEditorActions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RichTextEditorActions.swift; sourceTree = "<group>"; };
		9451478B2E2B9D04008FA284 /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		AF9826442E2F23470040BA71 /* FolderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FolderView.swift; sourceTree = "<group>"; };
		AF9826482E2F24540040BA71 /* MoveFolderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MoveFolderView.swift; sourceTree = "<group>"; };
		AF98264A2E2F262D0040BA71 /* NoteViewModel+Folder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "NoteViewModel+Folder.swift"; sourceTree = "<group>"; };
		AF98264C2E2F2FA80040BA71 /* NoteView+Empty.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "NoteView+Empty.swift"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		945147342E2B378F008FA284 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		9451472E2E2B378F008FA284 = {
			isa = PBXGroup;
			children = (
				9451474B2E2B3797008FA284 /* SmartPrompter */,
				945147382E2B378F008FA284 /* Products */,
			);
			sourceTree = "<group>";
		};
		945147382E2B378F008FA284 /* Products */ = {
			isa = PBXGroup;
			children = (
				945147372E2B378F008FA284 /* SmartPrompter.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		9451474B2E2B3797008FA284 /* SmartPrompter */ = {
			isa = PBXGroup;
			children = (
				9451474A2E2B3797008FA284 /* SmartPrompterApp.swift */,
				945147682E2B4284008FA284 /* App */,
				945147512E2B37AD008FA284 /* Assets */,
				945147502E2B37A1008FA284 /* Module */,
			);
			path = SmartPrompter;
			sourceTree = "<group>";
		};
		945147502E2B37A1008FA284 /* Module */ = {
			isa = PBXGroup;
			children = (
				AF9826432E2F233E0040BA71 /* Folder */,
				9451475F2E2B37C3008FA284 /* Note */,
				945147792E2B80CC008FA284 /* EditNote */,
			);
			path = Module;
			sourceTree = "<group>";
		};
		945147512E2B37AD008FA284 /* Assets */ = {
			isa = PBXGroup;
			children = (
				945147472E2B3797008FA284 /* Assets.xcassets */,
			);
			path = Assets;
			sourceTree = "<group>";
		};
		945147552E2B37C3008FA284 /* Managers */ = {
			isa = PBXGroup;
			children = (
				945147522E2B37C3008FA284 /* FolderManager.swift */,
				945147532E2B37C3008FA284 /* NoteManager.swift */,
				945147542E2B37C3008FA284 /* ScriptManager.swift */,
				945147582E2B37C3008FA284 /* RecentlyDeletedManager.swift */,
				945147592E2B37C3008FA284 /* RichTextUtils.swift */,
			);
			path = Managers;
			sourceTree = "<group>";
		};
		945147572E2B37C3008FA284 /* Models */ = {
			isa = PBXGroup;
			children = (
				945147562E2B37C3008FA284 /* ScriptModel.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		9451475A2E2B37C3008FA284 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				9451476B2E2B479D008FA284 /* NoteViewModel.swift */,
				944010D22E2CB1A600236C1E /* NoteViewModel+List.swift */,
				944010D42E2CB1D800236C1E /* NoteViewModel+Note.swift */,
				AF98264A2E2F262D0040BA71 /* NoteViewModel+Folder.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		9451475D2E2B37C3008FA284 /* Views */ = {
			isa = PBXGroup;
			children = (
				9451475C2E2B37C3008FA284 /* NoteView.swift */,
				945147732E2B7F20008FA284 /* NoteView+Action.swift */,
				9451476F2E2B481D008FA284 /* NoteView+TopBar.swift */,
				945147712E2B5C3D008FA284 /* NoteView+List.swift */,
				AF98264C2E2F2FA80040BA71 /* NoteView+Empty.swift */,
				9451476D2E2B480C008FA284 /* NoteView+BottomBar.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		9451475F2E2B37C3008FA284 /* Note */ = {
			isa = PBXGroup;
			children = (
				945147552E2B37C3008FA284 /* Managers */,
				945147572E2B37C3008FA284 /* Models */,
				9451475A2E2B37C3008FA284 /* ViewModel */,
				9451475D2E2B37C3008FA284 /* Views */,
				9451475E2E2B37C3008FA284 /* README.md */,
			);
			path = Note;
			sourceTree = "<group>";
		};
		945147682E2B4284008FA284 /* App */ = {
			isa = PBXGroup;
			children = (
				945147852E2BD43E008FA284 /* AppButtonStyles.swift */,
				945147862E2BD43E008FA284 /* AppIcons.swift */,
				945147692E2B4292008FA284 /* AppConstrants.swift */,
				945147752E2B7F67008FA284 /* AppHaptic.swift */,
				945147772E2B8082008FA284 /* AppLog.swift */,
				944010D02E2CACC600236C1E /* AppMenuManager.swift */,
			);
			path = App;
			sourceTree = "<group>";
		};
		945147792E2B80CC008FA284 /* EditNote */ = {
			isa = PBXGroup;
			children = (
				945147822E2B9C60008FA284 /* Views */,
				9451477A2E2B80D4008FA284 /* EditNoteView.swift */,
				9451477C2E2B8518008FA284 /* EditNoteView+Action.swift */,
				9451477E2E2B8575008FA284 /* EditNoteView+TopBar.swift */,
				945147802E2B9263008FA284 /* EditNoteView+TextView.swift */,
				9451478B2E2B9D04008FA284 /* README.md */,
			);
			path = EditNote;
			sourceTree = "<group>";
		};
		945147822E2B9C60008FA284 /* Views */ = {
			isa = PBXGroup;
			children = (
				945147832E2B9C6A008FA284 /* RichTextEditor.swift */,
				945147852E2B9D01008FA284 /* RichTextToolbar.swift */,
				945147872E2B9D02008FA284 /* RichTextEditorActions.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		AF9826432E2F233E0040BA71 /* Folder */ = {
			isa = PBXGroup;
			children = (
				AF9826442E2F23470040BA71 /* FolderView.swift */,
				AF9826482E2F24540040BA71 /* MoveFolderView.swift */,
			);
			path = Folder;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		945147362E2B378F008FA284 /* SmartPrompter */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 945147442E2B3790008FA284 /* Build configuration list for PBXNativeTarget "SmartPrompter" */;
			buildPhases = (
				945147332E2B378F008FA284 /* Sources */,
				945147342E2B378F008FA284 /* Frameworks */,
				945147352E2B378F008FA284 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SmartPrompter;
			packageProductDependencies = (
			);
			productName = SmartPrompter;
			productReference = 945147372E2B378F008FA284 /* SmartPrompter.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		9451472F2E2B378F008FA284 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					945147362E2B378F008FA284 = {
						CreatedOnToolsVersion = 16.4;
					};
				};
			};
			buildConfigurationList = 945147322E2B378F008FA284 /* Build configuration list for PBXProject "SmartPrompter" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 9451472E2E2B378F008FA284;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 945147382E2B378F008FA284 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				945147362E2B378F008FA284 /* SmartPrompter */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		945147352E2B378F008FA284 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				945147602E2B37C3008FA284 /* README.md in Resources */,
				9451474C2E2B3797008FA284 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		945147332E2B378F008FA284 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				944010D52E2CB1D800236C1E /* NoteViewModel+Note.swift in Sources */,
				945147722E2B5C3D008FA284 /* NoteView+List.swift in Sources */,
				9451476C2E2B479D008FA284 /* NoteViewModel.swift in Sources */,
				9451476A2E2B4292008FA284 /* AppConstrants.swift in Sources */,
				9451474F2E2B3797008FA284 /* SmartPrompterApp.swift in Sources */,
				AF9826492E2F24540040BA71 /* MoveFolderView.swift in Sources */,
				945147762E2B7F67008FA284 /* AppHaptic.swift in Sources */,
				AF9826452E2F23470040BA71 /* FolderView.swift in Sources */,
				9451476E2E2B480C008FA284 /* NoteView+BottomBar.swift in Sources */,
				945147702E2B481D008FA284 /* NoteView+TopBar.swift in Sources */,
				945147872E2BD43E008FA284 /* AppButtonStyles.swift in Sources */,
				945147882E2BD43E008FA284 /* AppIcons.swift in Sources */,
				945147742E2B7F20008FA284 /* NoteView+Action.swift in Sources */,
				945147812E2B9263008FA284 /* EditNoteView+TextView.swift in Sources */,
				9451477D2E2B8518008FA284 /* EditNoteView+Action.swift in Sources */,
				945147612E2B37C3008FA284 /* ScriptManager.swift in Sources */,
				945147622E2B37C3008FA284 /* FolderManager.swift in Sources */,
				944010D32E2CB1A600236C1E /* NoteViewModel+List.swift in Sources */,
				944010D12E2CACC600236C1E /* AppMenuManager.swift in Sources */,
				9451477B2E2B80D4008FA284 /* EditNoteView.swift in Sources */,
				945147782E2B8082008FA284 /* AppLog.swift in Sources */,
				945147632E2B37C3008FA284 /* NoteView.swift in Sources */,
				945147842E2B9C6A008FA284 /* RichTextEditor.swift in Sources */,
				945147862E2B9D01008FA284 /* RichTextToolbar.swift in Sources */,
				945147882E2B9D02008FA284 /* RichTextEditorActions.swift in Sources */,
				945147642E2B37C3008FA284 /* RichTextUtils.swift in Sources */,
				AF98264D2E2F2FA80040BA71 /* NoteView+Empty.swift in Sources */,
				945147652E2B37C3008FA284 /* NoteManager.swift in Sources */,
				9451477F2E2B8575008FA284 /* EditNoteView+TopBar.swift in Sources */,
				945147662E2B37C3008FA284 /* RecentlyDeletedManager.swift in Sources */,
				AF98264B2E2F262D0040BA71 /* NoteViewModel+Folder.swift in Sources */,
				945147672E2B37C3008FA284 /* ScriptModel.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		945147422E2B3790008FA284 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = T7S57R5XTP;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		945147432E2B3790008FA284 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = T7S57R5XTP;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		945147452E2B3790008FA284 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = T7S57R5XTP;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ryan.SmartPrompter;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		945147462E2B3790008FA284 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = T7S57R5XTP;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ryan.SmartPrompter;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		945147322E2B378F008FA284 /* Build configuration list for PBXProject "SmartPrompter" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				945147422E2B3790008FA284 /* Debug */,
				945147432E2B3790008FA284 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		945147442E2B3790008FA284 /* Build configuration list for PBXNativeTarget "SmartPrompter" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				945147452E2B3790008FA284 /* Debug */,
				945147462E2B3790008FA284 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 9451472F2E2B378F008FA284 /* Project object */;
}
